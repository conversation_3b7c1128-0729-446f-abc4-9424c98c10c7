{
	"version": "",
	"DBDairy": {
		"ConnectionStrings": {
			"MySQL": "persistsecurityinfo=True;port=3306;Server=mysql;Database=cashierln;user id=root;password=*********;SslMode=none",
			"SQLServer": "data source=localhost;initial catalog=test;user id=sa;password=**************;multipleactiveresultsets=True"
		},
		"DBSelected": "MySQL",
		"CassandraHost": "localhost",
		"KeySpace": "ncubo_keyspace",
		"UseCassandra": "false",
		"MonthsToArchivedMovements": 24
	},
	"Ejemplo ActionsBeforeRecovering": "company.LastKnownCompleteGradedDate = 11/01/2019;",
	"ActionsBeforeRecovering": "",
	"IsFollower": false,
	"MockToStart": 0,
	"Vaults": {
		"Url": "http://localhost:32770/"
	},
	"Security": {
		"ForceAuthority": false,
		"Clerks": {
			"Api": "http://*************:8081/auth/admin/realms/Clerks",
			"Authority": "http://*************:8081/auth/realms/Clerks",
			"Certificate": "MIICmzCCAYMCBgGO7LO1hzANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZDbGVya3MwHhcNMjQwNDE3MTUzNDQ0WhcNMzQwNDE3MTUzNjI0WjARMQ8wDQYDVQQDDAZDbGVya3MwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCeVNrMBXHsVzYgj0yPKzkWSmMp29mBhC7Oa+08or7yyIIZwr+9f3+9rJpD19EfIRuR+yqw4zRM3cngACxWYY6oWjhcjfnGtoeV7xSSCt4iBRFE3ceJRmr4WGLGz/r5aaD1Q03Xn/Hjiql9i3fb2u4/Tjv+8WIP8GHdHoo4GL4Z3LY28u1mVi0R987efYSHlDxBCCUgu6geQAHnlroT9EWp54WpdkuVRYGn11mGJn8aTLuBaamVJ3asuRIhWY6q1mI9c062TQaQQxVsoWnxJak04Nl/Y1eXou3HMauUv52hxnfHJgciMxHrVtL1Rhg/ypyw4MLrjGUCo0P4vdnMyRdRAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAFahYlW/RVOk2XCyTGXYSWSbjlMx0it/CdBE6D5pfA8SQPfo8sgxaLIFbAvuWwolWxawAsXUecVz26NTeLNmMoFcvUSd34Nc00hvRBkq0mpIirSZFBlWwhW7rMTFIDyPT9EJ3NTgaeV1r6465CY0KeQuUAC2/JzC7frXvg0MvQC3PvyGinQubL/CU+nSnkvCN5X5f2qh/NUqUHtohcMjZKRoTlPL3wLnHHQsdyUNTJLUgVawUsNq4NrZJ+d8BouF2AeCA2EAh3tuwTbZGv8S1LEsnzYvXhIMAIq6tFZNVb5GyTHNiQZ+KxUtlyCXYkAG1uRkxvYNlthUS0EtYJo5Rxk=",
			"PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnlTazAVx7Fc2II9Mjys5FkpjKdvZgYQuzmvtPKK+8siCGcK/vX9/vayaQ9fRHyEbkfsqsOM0TN3J4AAsVmGOqFo4XI35xraHle8UkgreIgURRN3HiUZq+Fhixs/6+Wmg9UNN15/x44qpfYt329ruP047/vFiD/Bh3R6KOBi+Gdy2NvLtZlYtEffO3n2Eh5Q8QQglILuoHkAB55a6E/RFqeeFqXZLlUWBp9dZhiZ/Gky7gWmplSd2rLkSIVmOqtZiPXNOtk0GkEMVbKFp8SWpNODZf2NXl6LtxzGrlL+docZ3xyYHIjMR61bS9UYYP8qcsODC64xlAqND+L3ZzMkXUQIDAQAB",
			"Clients": [
				{
					"Name": "internal_admin",
					"Id": "c96f710b-913e-4fb1-a842-c4dc9ba113e0",
					"DefaultUser": "lottoadmin",
					"DefaultPassword": "123456"
				},
				{
					"Name": "lotto",
					"Id": "1387b079-a242-478e-9b51-69dd5edf19d3",
					"DefaultUser": "lottoadmin",
					"DefaultPassword": "123456"
				},
				{
					"Name": "exchange",
					"Id": "22f656dc-8054-405b-b382-97a41ba3d8ae",
					"DefaultUser": "lottoadmin",
					"DefaultPassword": "123456"
				}
			]
		},
		"Players": {
			"Api": "http://*************:8081/auth/admin/realms/Players",
			"Authority": "http://*************:8081/auth/realms/Players",
			"Certificate": "MIICnTCCAYUCBgGO7LMoYzANBgkqhkiG9w0BAQsFADASMRAwDgYDVQQDDAdQbGF5ZXJzMB4XDTI0MDQxNzE1MzQwOFoXDTM0MDQxNzE1MzU0OFowEjEQMA4GA1UEAwwHUGxheWVyczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMKcKenjpa9Ws19JizteMiZQwvR0Rm3ysuFXcaWsKBtQmSGMoUaIgNB33oBdNc5yPjeY/CkQHmL5epmG1+N2iUYdc9mB/jDjrCu4MuFZ4LmTdzsTLaqFDw0+Jkt7JGSZW5x0ytrGZ1sCB3VVAbJzvBOeQ7rNGeekNEmxvBKcpBZXVZDPfyjQdJ71K7JW4zTYmdriFEoa2JcdCTNlBAK+VBsyL45/lTe9dV1AiBH7DDVpn/vUDuUmFKQtwnYx2qPjdwGqQTofYhcQM/NTB7kK7kP0pt115TPVPDEGS15grPSU/Pr5ZNou8IrOpxQ0trvYUUz1yit/Cetv3Umu6k7l9NMCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAr7HSojIUEpSdHOzeQBwBFkyJpJIboriW9C6zk27hOCHI1QDg7VxdkOIRd9mUSXMfrzihsBuP2TKO0+xCoHki6+5Y7F2WFXc58ZAuQNwjK69JlR2xzi12UckPnO3qK+XtUhy31niyB/7ERZsWoauCYeot6fTsrt7CNNHdGCYJXduAkGJwc7grHspWAcAiAgXL5Dw23DyXSQecSPCw3YMOrISkwr4jHOcmeLycSNeNSC3eNZu4rDmSND0gcAQOdrKNyZkp218LVAk8lml0bALUfuYnVeLT0FFBmqdD7+7o8HXAIJZtPJ79YHXhceMagWkiwd0Q4+sa6+HzeOIcH+XL8w==",
			"PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwpwp6eOlr1azX0mLO14yJlDC9HRGbfKy4VdxpawoG1CZIYyhRoiA0HfegF01znI+N5j8KRAeYvl6mYbX43aJRh1z2YH+MOOsK7gy4VnguZN3OxMtqoUPDT4mS3skZJlbnHTK2sZnWwIHdVUBsnO8E55Dus0Z56Q0SbG8EpykFldVkM9/KNB0nvUrslbjNNiZ2uIUShrYlx0JM2UEAr5UGzIvjn+VN711XUCIEfsMNWmf+9QO5SYUpC3CdjHao+N3AapBOh9iFxAz81MHuQruQ/Sm3XXlM9U8MQZLXmCs9JT8+vlk2i7wis6nFDS2u9hRTPXKK38J62/dSa7qTuX00wIDAQAB",
			"Clients": [
				{
					"Name": "lotto",
					"Id": "03c83749-5dc3-4552-b435-8a660fbed81b",
					"DefaultUser": "lottoadmin",
					"DefaultPassword": "123456"
				},
				{
					"Name": "exchange",
					"Id": "217c2da0-282e-4b69-9890-db40df9f9562",
					"DefaultUser": "lottoadmin",
					"DefaultPassword": "123456"
				}
			]
		}
	},
	"Logging": {
		"IncludeScopes": false,
		"Debug": {
			"LogLevel": {
				"Default": "Warning"
			}
		},
		"Console": {
			"LogLevel": {
				"Default": "Warning"
			}
		}
	},
	"BIIntegration": {
		"useKafka": true,
		"useDb": false,
		"kafka": {
			"server": "queue.production:9092",
			"topicForLottoGrading": "LottoGrading",
			"topicForTransacctions": "Transactions",
			"topicForGrades": "LottoGrades",
			"topicForDrawings": "LottoDrawings",
			"topicForRecents": "Recents",
			"topicForDomains": "Domains",
			"topicForStoreRegistration": "StoreRegistration",
			"topicForCustomSettings": "CustomSettings",
			"topicForCatalog": "Catalog",
			"topicForCustomers": "Customers",
			"topicForNotifications": "Notifications",
			"topicForProfanity": "Profanity",
			"topicForScheduler": "Scheduler",
			"topicForPrizes": "LottoPrizes",
			"topicForFragments": "Fragments",
			"topicForPayFragments": "PayFragments",
			"topicForMovements": "Movements",
			"topicForDeposits": "Deposits",
			"topicForWithdrawals": "Withdrawals",
			"topicForChallenges": "Challenges",
			"topicForBalances": "Balances",
			"topicForIncomingOperations": "IncomingOperations",
			"topicForOutgoingOperations": "OutgoingOperations",
			"topicForGuardian": "guardian",
			"topicForLinesTags": "LinesTags",
			"topicForLinesScores": "LinesScores",
			"topicForLinesGrading": "LinesGrading",
			"topicForLinesETL": "EtlInfo",
			"group": "games"
		},
		"DBHistorical": {
			"ConnectionStrings": {
				"MySQL": "persistsecurityinfo=True;port=3306;Server=mysql;Database=cashierln;user id=root;password=*********;SslMode=none",
				"SQLServer": "data source=localhost;initial catalog=test;user id=sa;password=**************;multipleactiveresultsets=True"
			},
			"DBSelected": "MySQL"
		}
	},
	"ErrorsSender": {
		"SmtpServer": "smtp.gmail.com",
		"SmtpPort": 587,
		"SmtpUsername": "<EMAIL>",
		"SmtpPassword": "Rdiaz123",
		"UseSender": true,
		"SendTo": ""
	},
	"ElasticApm": {
		"useApm": false,
		"SecretToken": "",
		"ServerUrls": "http://apm:8200", //Set custom APM Server URL (default: http://localhost:8200)
		"ServiceName": "CashierAPI", //allowed characters: a-z, A-Z, 0-9, -, _, and space. Default is the entry assembly of the application
		"Environment": "production" // Set the service environment
	},
	"MessageOriginId": 11
}