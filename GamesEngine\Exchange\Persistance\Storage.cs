﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Text;
using static GamesEngine.Exchange.BalanceMessage;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Exchange.Persistance
{
    public class Storage
    {
        private readonly string connectionString;

        public Storage()
        {
            if (Integration.Db.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                connectionString = Integration.Db.MySQL;
            }
            else
            {
                throw new GameEngineException($"There is no connection for {Integration.Db.DBSelected}");
            }
        }

		public void SaveVerifiedBatches(BatchBalancesMessage message, DateTime date, int type)
		{
			if (message == null) throw new ArgumentNullException(nameof(message));

			var sql = $@"INSERT INTO verifiedbatches(DATE, TYPE, CURRENCYID, INITIAL, AVAILABLE, LOCKED, LOCKEDACCUMULATED, SPENDACCUMULATED, BATCHNUMBER) 
						VALUES(@date, @type, @currencyId, @initial, @available, @locked, @lockedAccumulated, @spendAccumulated, @batchNumber); ";
			try
			{
				try
				{
					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.Parameters.AddWithValue("@date", date);
							command.Parameters.AddWithValue("@type", type);
							command.Parameters.AddWithValue("@currencyId", message.CurrencyId);
							command.Parameters.AddWithValue("@initial", message.Initial);
							command.Parameters.AddWithValue("@available", message.Available);
							command.Parameters.AddWithValue("@locked", message.Locked);
							command.Parameters.AddWithValue("@lockedAccumulated", message.LockedAccumulated);
							command.Parameters.AddWithValue("@spendAccumulated", message.SpendAccumulated);
							command.Parameters.AddWithValue("@batchNumber", message.BatchNumber);
							command.ExecuteNonQuery();
						}
						connection.Close();
					}
				}
				catch (Exception e)
				{
					string msg = $@"sql:{sql} @batchNumber:{message.BatchNumber} @currencyId:{message.CurrencyId}";
					Loggers.GetIntance().Db.Error(msg, e.InnerException ?? e);
					ErrorsSender.Send(e.InnerException ?? e, msg);
					throw;
				}

				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql, "verifiedbatches", "ExchangeAPI");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql}", webhookEx);
				throw;
			}
		}

		public IEnumerable<VerifiedBatch> ListVerifiedBatches(DateTime startDate, DateTime endDate, int initialIndex, int amountOfRows=100)
        {
			var verifiedBatches = new List<VerifiedBatch>();
			var sql = $@"SELECT DATE, TYPE, CURRENCYID, INITIAL, AVAILABLE, LOCKED, LOCKEDACCUMULATED, SPENDACCUMULATED, BATCHNUMBER
						FROM verifiedbatches
						WHERE DATE(DATE) <= @endDate AND DATE(DATE) >= @startDate
						LIMIT @amountOfRows OFFSET @initialIndex;";
			try
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
						{
							command.Parameters.AddWithValue("@startDate", startDate);
							command.Parameters.AddWithValue("@endDate", endDate);
							command.Parameters.AddWithValue("@amountOfRows", amountOfRows);
							command.Parameters.AddWithValue("@initialIndex", initialIndex);
							var dataReader = command.ExecuteReader();

							while (dataReader.Read())
							{
								var date = dataReader.GetDateTime(0);
								var type = dataReader.GetString(1);
								var currencyId = dataReader.GetInt32(2);
								var initial = dataReader.GetDecimal(3);
								var available = dataReader.GetDecimal(4);
								var locked = dataReader.GetDecimal(5);
								var lockedAccumulated = dataReader.GetDecimal(6);
								var spendAccumulated = dataReader.GetDecimal(7);
								var batchNumber = dataReader.GetInt32(8);
								var verifiedBatch = new VerifiedBatch()
								{
									Date = date,
									Type = type,
									CurrencyId = currencyId,
									Initial = initial,
									Available = available,
									Locked = locked,
									LockedAccumulated = lockedAccumulated,
									SpendAccumulated = spendAccumulated,
									BatchNumber = batchNumber
								};
								verifiedBatches.Add(verifiedBatch);
							}
						}
					}
					finally
					{
						connection.Close();
					}
				}
				return verifiedBatches;
			}
			catch (Exception e)
			{
				string message = $@"sql:{sql} @startDate:{startDate} @endDate:{endDate}";
				Loggers.GetIntance().Db.Error(message, e.InnerException ?? e);
				ErrorsSender.Send(e.InnerException ?? e, message);
				throw;
			}
		}
	}
}
