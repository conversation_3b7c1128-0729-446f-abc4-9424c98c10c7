﻿using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Text;
using town.connectors.drivers;
using static GamesEngine.Bets.PlayerLottoReports;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Games.Lotto.Lottery;

namespace GamesEngine.Games.Lotto
{
	public class QueryMakerOfHistoricalPicks : QueryMakerOfHistorical
	{
		public const string ID_PICK2 = "P2";
		public const string ID_PICK3 = "P3";
		public const string ID_PICK4 = "P4";
		public const string ID_PICK5 = "P5";
		public const string ID_POWERBALL = "PB";

		readonly LottoD<PERSON><PERSON><PERSON><PERSON><PERSON>icks lottoDBHandler;

		public QueryMakerOfHistoricalPicks()
		{
			if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
			{
				lottoDBHandler = new LottoDBHandlerMySQL(Integration.Db.MySQL);
			}
			else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
			{
				lottoDBHandler = new LottoDBHandlerSQLServer(Integration.Db.SQLServer);
			}
			else
			{
#if DEBUG
				lottoDBHandler = new LottoDBHandlerInMemory();
#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}
		}

		internal QueryMakerOfHistoricalPicks(Company company):base(company)
		{
			if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
			{
				lottoDBHandler = new LottoDBHandlerMySQL(this.company, Integration.Db.MySQL);
			}
			else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
			{
				lottoDBHandler = new LottoDBHandlerSQLServer(this.company, Integration.Db.SQLServer);
			}
			else
			{
#if DEBUG
				lottoDBHandler = new LottoDBHandlerInMemory(this.company);
#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
			}
		}

		private IEnumerable<WinnerInfo> FilterWinnerTickets(IEnumerable<WinnerInfo> winners, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			var winnerInfos = new List<WinnerInfo>();
			foreach (var winner in winners)
			{
				if (((winner.TicketTypeForReports & validTicketTypeForReports) != 0) && (winner.WasPurchasedForFree == withLR))
				{
					winnerInfos.Add(winner);
				}
			}
			return winnerInfos;
		}

		internal IEnumerable<TotalProfitByDrawingRecord> AccumulateTotalProfitByDrawingRecords(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers, IEnumerable<WinnerInfo> noActions)
		{
			if (winners == null) throw new ArgumentNullException(nameof(winners));
			if (losers == null) throw new ArgumentNullException(nameof(losers));
			if (noActions == null) throw new ArgumentNullException(nameof(noActions));

			var result = lottoDBHandler.AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);
			return result;
		}

		internal IEnumerable<DailyTotalProfitRecord2> AccumulateDailyTotalProfitRecords2(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers)
		{
			if (winners == null) throw new ArgumentNullException(nameof(winners));
			if (losers == null) throw new ArgumentNullException(nameof(losers));

			var result = lottoDBHandler.AccumulateDailyTotalProfitRecords2(winners, losers);
			return result;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
		{
			if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.WinnerTicketsOfPlayerBetween(startDate, endDate, accountNumber);
			return result;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
		{
			if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.LoserTicketsOfPlayerBetween(startDate, endDate, accountNumber);
			return result;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
		{
			if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.NoActionTicketsOfPlayerBetween(startDate, endDate, accountNumber);
			return result;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.WinnerTicketsOfPlayer(drawDate, state, accountNumber);
			return result;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.LoserTicketsOfPlayer(drawDate, state, accountNumber);
			return result;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.NoActionTicketsOfPlayer(drawDate, state, accountNumber);
			return result;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeAt(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByTimeAt(date, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByTimeFrom(date, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (dates.Count == 0) throw new GameEngineException("At least one date is required");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByTimeIn(dates, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByTimeBetween(startedDate, endedDate, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateAt(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByStateAt(date, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateFrom(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByStateFrom(date, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (dates.Count == 0) throw new GameEngineException("At least one date is required");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByStateIn(dates, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var winners = lottoDBHandler.WinnerTicketsOfPlayerByStateBetween(startedDate, endedDate, accountNumber);
			var winnerInfos = FilterWinnerTickets(winners, validTicketTypeForReports, withLR);
			return winnerInfos;
		}

		internal IEnumerable<WinnerInfo> GetPlayedTicketsBy(string ticketNumber)
		{
			var result = lottoDBHandler.GetPlayedTicketsBy(ticketNumber);
			return result;
		}

		internal WinnerInfo GetWinnerTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
		{
			var result = lottoDBHandler.GetWinnerTicketBy(state, creationDate, drawDate, accountNumber);
			return result;
		}

		internal WinnerInfo WinnerTicketBy(string state, DateTime drawDate, string ticketNumber)
		{
			var result = lottoDBHandler.WinnerTicketBy(state, drawDate, ticketNumber);
			return result;
		}

		private IEnumerable<LoserInfo> FilterLoserTickets(IEnumerable<LoserInfo> losers, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			var loserInfos = new List<LoserInfo>();
			foreach (var loser in losers)
			{
				if (((loser.TicketTypeForReports & validTicketTypeForReports) != 0) && (loser.WasPurchasedForFree == withLR))
				{
					loserInfos.Add(loser);
				}
			}
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeAt(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByTimeAt(date, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByTimeFrom(date, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (dates.Count == 0) throw new GameEngineException("At least one date is required");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByTimeIn(dates, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByTimeBetween(startedDate, endedDate, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateAt(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByStateAt(date, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateFrom(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByStateFrom(date, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (dates.Count == 0) throw new GameEngineException("At least one date is required");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByStateIn(dates, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber, TicketTypeForReports validTicketTypeForReports, bool withLR)
		{
			if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var losers = lottoDBHandler.LoserTicketsOfPlayerByStateBetween(startedDate, endedDate, accountNumber);
			var loserInfos = FilterLoserTickets(losers, validTicketTypeForReports, withLR);
			return loserInfos;
		}

		internal LoserInfo GetLoserTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
		{
			var result = lottoDBHandler.GetLoserTicketBy(state, creationDate, drawDate, accountNumber);
			return result;
		}

		internal LoserInfo LoserTicketBy(string state, DateTime drawDate, string ticketNumber)
		{
			var result = lottoDBHandler.LoserTicketBy(state, drawDate, ticketNumber);
			return result;
		}

		private IEnumerable<NoActionInfo> FilterNoActionTickets(IEnumerable<NoActionInfo> noActions, TicketTypeForReports validTicketTypeForReports)
		{
			var noActionInfos = new List<NoActionInfo>();
			foreach (var noAction in noActions)
			{
				if ((noAction.TicketTypeForReports & validTicketTypeForReports) != 0)
				{
					noActionInfos.Add(noAction);
				}
			}
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeAt(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByTimeAt(date, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByTimeFrom(date, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (dates.Count == 0) throw new GameEngineException("At least one date is required");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByTimeIn(dates, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByTimeBetween(startedDate, endedDate, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateAt(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByStateAt(date, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateFrom(DateTime date, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByStateFrom(date, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (dates.Count == 0) throw new GameEngineException("At least one date is required");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByStateIn(dates, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber, TicketTypeForReports validTicketTypeForReports)
		{
			if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var noActions = lottoDBHandler.NoActionTicketsOfPlayerByStateBetween(startedDate, endedDate, accountNumber);
			var noActionInfos = FilterNoActionTickets(noActions, validTicketTypeForReports);
			return noActionInfos;
		}

		internal NoActionInfo GetNoActionTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
		{
			var result = lottoDBHandler.GetNoActionTicketBy(state, creationDate, drawDate, accountNumber);
			return result;
		}

		internal NoActionInfo NoActionTicketBy(string state, DateTime drawDate, string accountNumber)
		{
			var result = lottoDBHandler.NoActionTicketBy(state, drawDate, accountNumber);
			return result;
		}

		internal CompletedPicksDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
			return result;
		}

		internal List<AffiliateData> ListAffiliates()
		{
			var result = lottoDBHandler.ListAffiliates();
			return result;
		}

		public void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
		{
			try
			{
				try
				{
					lottoDBHandler.InsertAffiliateIfNotExist(id, name, accountNumber);
				}
				catch (Exception e)
				{
					throw;
				}

				// After successful DB operation, send to webhook
				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, $"INSERT AFFILIATE: ID={id}, NAME={name}, ACCOUNT={accountNumber}", "affiliates", "LottoAPI");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for affiliate insert: ID={id}, NAME={name}, ACCOUNT={accountNumber}", webhookEx);
				throw;
			}
		}

		internal TicketsPerPlayersInCompletedPicksDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateTicketsPerPlayersInDrawingReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
			return result;
		}

		internal WagersPerPlayerInCompletedDraw GenerateWagersPerPlayerInDrawingReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string fullTicketNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateWagersPerPlayerInDrawingReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, fullTicketNumber, domainIds);
			return result;
		}

		internal IEnumerable<PickWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateWinnersReport(startDate, endDate, accountNumber, domainIds);
			return result;
		}

		internal IEnumerable<WinnerInfo> TopWinnersForDrawing(DateTime drawDate, string state, string gameType, string domainUrl)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");

			var result = lottoDBHandler.TopWinnersForDrawing(drawDate, state, gameType, domainUrl);
			return result;
		}

		public IEnumerable<PickWinnerRecord> GenerateWinnersOfTheMonthReport(DateTime date)
		{
			if (date == default(DateTime)) throw new GameEngineException($"{nameof(date)} cannot have default value");

			var result = lottoDBHandler.GenerateWinnersOfTheMonthReport(date);
			return result;
		}

		public string DrawingNameFor(int drawId)
		{
			if (drawId < 0) throw new GameEngineException($"Drawing Id {drawId} does not exist");

			var result = lottoDBHandler.DrawingNameFor(drawId);
			return result;
		}

		internal CompletedLastPicksDraws LastPlayedDrawingsOfPlayer(string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

			var result = lottoDBHandler.LastPlayedDrawingsOfPlayer(accountNumber);
			return result;
		}

		internal bool ExistsDailyTotalProfitStorage()
		{
			var result = lottoDBHandler.ExistsTable(LottoDBHandlerPicks.TABLE_DAILY_TOTAL_PROFIT2);
			return result;
		}

		internal bool ExistsTotalProfitByDrawingStorage()
		{
			var result = lottoDBHandler.ExistsTable(LottoDBHandlerPicks.TABLE_TOTAL_PROFIT_BY_DRAWING);
			return result;
		}
		internal bool ExistsDailyTotalProfitStorage2()
		{
			var result = lottoDBHandler.ExistsDailyTotalProfitStorage2();
			return result;
		}

		internal override DateTime LastDateInDailyTotalProfit()
		{
			var result = lottoDBHandler.LastDateInDailyTotalProfit();
			return result;
		}

		internal DateTime LastDateInTotalProfitByDrawing()
		{
			var result = lottoDBHandler.LastDateInTotalProfitByDrawing();
			return result;
		}

		internal DailyTotalProfitReport GenerateDailyTotalProfitReport(DateTime startDate, DateTime endDate, DateTime now, string gameType, string domainIds, int currencyId)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateDailyTotalProfitReport(startDate, endDate, now, gameType, domainIds, currencyId);
			return result;
		}

		internal TotalProfitByDrawingReport GenerateTotalProfitByDrawingReport(DateTime startDate, DateTime endDate, DateTime now, string gameType, string uniqueDrawingId, string domainIds)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateTotalProfitByDrawingReport(startDate, endDate, now, gameType, uniqueDrawingId, domainIds);
			return result;
		}

		internal override void AccumulateTotalProfitByDrawing(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException($"{nameof(now)} cannot have default value");

			lottoDBHandler.AccumulateTotalProfitByDrawing(now);
		}

		internal override List<DailyTotalProfitRecord2> AccumulateDailyTotalProfit(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException($"{nameof(now)} cannot have default value");

			return lottoDBHandler.AccumulateDailyTotalProfit2(now);
		}

		internal void AccumulateTotalProfitByDrawing(DateTime startDate, DateTime endDate, DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException($"{nameof(now)} cannot have default value");
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			lottoDBHandler.AccumulateTotalProfitByDrawing(startDate, endDate, now);
		}

		internal void UpdateDailyTotalProfit2(DateTime dateWithoutTime, string gameType)
		{
			if (dateWithoutTime == default(DateTime)) throw new GameEngineException($"{nameof(dateWithoutTime)} cannot have default value");
			if (string.IsNullOrEmpty(gameType)) throw new GameEngineException($"Parameter {nameof(gameType)} is required");
			if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException("Day to update table can not have hours or minutes.");
			if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

			try
			{
				try
				{
					lottoDBHandler.UpdateDailyTotalProfit2(dateWithoutTime, gameType);
				}
				catch (Exception e)
				{
					throw;
				}

				// After successful DB operation, send to webhook
				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, $"UPDATE DailyTotalProfit2: DATE={dateWithoutTime:yyyy-MM-dd}, GAMETYPE={gameType}", "l900dailytotalprofit2", "LottoAPI");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for UpdateDailyTotalProfit2: DATE={dateWithoutTime:yyyy-MM-dd}, GAMETYPE={gameType}", webhookEx);
				throw;
			}
		}

		internal DailyTotalProfitReport UpdateDailyTotalProfitAt(DateTime day)
		{
			if (day == default(DateTime)) throw new GameEngineException($"{nameof(day)} cannot have default value");
			if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException("Day to update table can not have hours or minutes.");

			var result = lottoDBHandler.UpdateDailyTotalProfitAt(day);
			return result;
		}

		internal void UpdateTotalProfitByDrawing(DateTime drawDate, string state, string gameType)
		{
			if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
			if (string.IsNullOrEmpty(state)) throw new GameEngineException($"Parameter {nameof(state)} is required");
			if (string.IsNullOrEmpty(gameType)) throw new GameEngineException($"Parameter {nameof(gameType)} is required");
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

			try
			{
				try
				{
					lottoDBHandler.UpdateTotalProfitByDrawing(drawDate, state, gameType);
				}
				catch (Exception e)
				{
					throw;
				}

				// After successful DB operation, send to webhook
				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, $"UPDATE TotalProfitByDrawing: DATE={drawDate:yyyy-MM-dd HH:mm}, STATE={state}, GAMETYPE={gameType}", "l900totalprofitbydrawing", "LottoAPI");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for UpdateTotalProfitByDrawing: DATE={drawDate:yyyy-MM-dd HH:mm}, STATE={state}, GAMETYPE={gameType}", webhookEx);
				throw;
			}
		}

		internal TotalProfitByDrawingReport UpdateTotalProfitByDrawingAt(DateTime day)
		{
			if (day == default(DateTime)) throw new GameEngineException($"{nameof(day)} cannot have default value");
			if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException("Day to update table can not have hours or minutes.");

			var result = lottoDBHandler.UpdateTotalProfitByDrawingAt(day);
			return result;
		}

		internal void UpdateDailyTotalProfitWithAffiliates(DateTime dateWithoutTime, string gameType)
		{
			if (dateWithoutTime == default(DateTime)) throw new GameEngineException($"{nameof(dateWithoutTime)} cannot have default value");
			if (string.IsNullOrEmpty(gameType)) throw new GameEngineException($"Parameter {nameof(gameType)} is required");
			if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException("Day to update table can not have hours or minutes.");
			if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

			try
			{
				try
				{
					lottoDBHandler.UpdateDailyTotalProfit2(dateWithoutTime, gameType);
				}
				catch (Exception e)
				{
					throw;
				}

				// After successful DB operation, send to webhook
				WebHookClientRequest.Instance.SendWebHook(DateTime.Now, $"UPDATE DailyTotalProfitWithAffiliates: DATE={dateWithoutTime:yyyy-MM-dd}, GAMETYPE={gameType}", "l900dailytotalprofit2", "LottoAPI");
			}
			catch (Exception webhookEx)
			{
				Loggers.GetIntance().Webhook.Error($"Webhook failed for UpdateDailyTotalProfitWithAffiliates: DATE={dateWithoutTime:yyyy-MM-dd}, GAMETYPE={gameType}", webhookEx);
				throw;
			}
		}

		internal IEnumerable<TicketRecord> GenerateMoneyInvestedReport(DateTime startDate, DateTime endDate)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateMoneyInvestedReport(startDate, endDate);
			return result;
		}

		internal SalesAndPrizesPerDayReport GenerateSalesAndPrizesPerDayReport(DateTime startDate, DateTime endDate)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateSalesAndPrizesPerDayReport(startDate, endDate);
			return result;
		}

		internal CountsOfTicketsPerDayAndGameReport GenerateCountsOfTicketsPerDayAndGameReport(DateTime startDate, DateTime endDate)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");

			var result = lottoDBHandler.GenerateCountsOfTicketsPerDayAndGameReport(startDate, endDate);
			return result;
		}


		private abstract class LottoDBHandlerPicks: LottoDBHandler
		{
			protected const string TABLE_LOOSERS = "l900loosers";
			protected const string TABLE_WINNERS = "l900winners";
			protected const string TABLE_NOACTIONS = "l900noactions";
			protected const string TABLE_DRAWINGS = "l900drawings";

			protected const string TABLE_DAILY_TOTAL_PROFIT = "l900dailytotalprofit";
			internal const string TABLE_TOTAL_PROFIT_BY_DRAWING = "l900totalprofitbydrawing";
			internal const string TABLE_DAILY_TOTAL_PROFIT2 = "l900dailytotalprofit2";
			
			protected const string COMMON_SELECT_FOR_WINNERS = "select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.position, domainid, url, currencyid";
			protected const string COMMON_SELECT_FOR_LOSERS = "select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prizesversion, drawingid, DR.position, domainid, url, currencyid";
			protected const string COMMON_SELECT_FOR_NOACTION = "select state, date, account, ticket, count, amount, selection, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, noactionby, prizesversion, drawingid, DR.position, domainid, url, currencyid";

			protected LottoDBHandlerPicks(string connectionString):base(connectionString)
			{
			}

			protected LottoDBHandlerPicks(Company company, string connectionString) : base(company, connectionString)
			{
			}

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeAt(DateTime date, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateFrom(DateTime date, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateAt(DateTime date, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> GetPlayedTicketsBy(string ticketNumber);

			internal abstract WinnerInfo GetWinnerTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber);

			internal abstract WinnerInfo WinnerTicketBy(string state, DateTime drawDate, string ticketNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeAt(DateTime date, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateFrom(DateTime date, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateAt(DateTime date, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract LoserInfo GetLoserTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber);

			internal abstract LoserInfo LoserTicketBy(string state, DateTime drawDate, string ticketNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeAt(DateTime date, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateFrom(DateTime date, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateAt(DateTime date, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract NoActionInfo GetNoActionTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber);

			internal abstract NoActionInfo NoActionTicketBy(string state, DateTime drawDate, string ticketNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);

			internal abstract IEnumerable<WinnerInfo> WinnerTicketsOfPlayer(DateTime drawDate, string state, string accountNumber);

			internal abstract IEnumerable<LoserInfo> LoserTicketsOfPlayer(DateTime drawDate, string state, string accountNumber);

			internal abstract IEnumerable<NoActionInfo> NoActionTicketsOfPlayer(DateTime drawDate, string state, string accountNumber);

			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);

			protected abstract IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);

			protected abstract IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime startDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);
			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime startDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);
			protected abstract IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime startDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);
			
			protected abstract IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);
			internal abstract CompletedPicksDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);

			internal abstract TicketsPerPlayersInCompletedPicksDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds);

			internal abstract WagersPerPlayerInCompletedDraw GenerateWagersPerPlayerInDrawingReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string fullTicketNumber, string domainIds);

			internal abstract IEnumerable<PickWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds);

			internal abstract IEnumerable<WinnerInfo> TopWinnersForDrawing(DateTime drawDate, string state, string gameType, string domainUrl);

			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds);

			public abstract IEnumerable<PickWinnerRecord> GenerateWinnersOfTheMonthReport(DateTime date);

			internal abstract CompletedLastPicksDraws LastPlayedDrawingsOfPlayer(string accountNumber);

			public abstract string DrawingNameFor(int drawingId);

			
			internal DailyTotalProfitReport GenerateDailyTotalProfitReport(DateTime startDate, DateTime endDate, DateTime now, string gameType, string domainIds, int currencyId)
			{
				var lastDateAccumulatedInDailyTotalProfit = LastDateInDailyTotalProfit2();
				var yesterday = now.AddDays(-1).Date;
                if (lastDateAccumulatedInDailyTotalProfit.AddDays(1).Date == yesterday) AccumulateDailyTotalProfit2(yesterday);

				var totalProfitRecordsForTodayAndTomorrow = new List<DailyTotalProfitRecord2>();
				var isIncludedTodayOrFutureDays = endDate.Date >= now.Date;
				if (isIncludedTodayOrFutureDays)
				{
					var startDateToFilter = startDate.Date > now.Date ? startDate : now;
					var winners = WinnerTicketsForDailyTotalProfit2(startDateToFilter, endDate, gameType, domainIds, currencyId);
					var losers = LoserTicketsForDailyTotalProfit2(startDateToFilter, endDate, gameType, domainIds, currencyId);
					totalProfitRecordsForTodayAndTomorrow = AccumulateDailyTotalProfitRecords2(winners, losers);
				}

				var dailyTotalProfitRecords = FilteredDailyTotalProfitRecords2(startDate, endDate, gameType, domainIds, currencyId);
                if (!Enum.TryParse(company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {company.Lotto900().RewardCurrency} does not exist");
                var report = currencyId == (int)currency ?
                    new DailyTotalProfitReportUSD(dailyTotalProfitRecords, totalProfitRecordsForTodayAndTomorrow, currency, rewardCurrency) :
                    new DailyTotalProfitReport(dailyTotalProfitRecords, totalProfitRecordsForTodayAndTomorrow);
                return report;
			}

			protected abstract List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command);

			protected abstract CompletedLastPicksDraws GetDrawings(string command, string accountNumber);

			internal TotalProfitByDrawingReport GenerateTotalProfitByDrawingReport(DateTime startDate, DateTime endDate, DateTime now, string gameType, string uniqueDrawingId, string domainIds)
			{
				AccumulateTotalProfitByDrawing(now.AddDays(-1));

				var totalProfitRecordsForToday = new List<TotalProfitByDrawingRecord>();
				var isIncludedTodayOrFutureDays = endDate.Date >= now.Date;
				if (isIncludedTodayOrFutureDays)
				{
					var winners = WinnerTicketsForDrawingsReport(now, endDate, uniqueDrawingId, string.Empty, gameType, string.Empty, domainIds);
					var losers = LoserTicketsForDrawingsReport(now, endDate, uniqueDrawingId, string.Empty, gameType, string.Empty, domainIds);
					var noActions = NoActionTicketsForDrawingsReport(now, endDate, uniqueDrawingId, string.Empty, gameType, string.Empty, domainIds);
					totalProfitRecordsForToday = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);
				}

				var totalProfitByDrawingRecords = FilteredTotalProfitByDrawingRecords(startDate, endDate, gameType, uniqueDrawingId, domainIds);
				var report = new TotalProfitByDrawingReport(totalProfitByDrawingRecords, totalProfitRecordsForToday, now);
				return report;
			}

			internal List<DailyTotalProfitRecord2> AccumulateDailyTotalProfit2(DateTime now)
			{
				var gameTypeAll = ALL;
				var domainIdAll = ALL;

				bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
				if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();

				var winners = WinnerTicketsForDailyTotalProfit2(now, gameTypeAll, domainIdAll);
				var losers = LoserTicketsForDailyTotalProfit2(now, gameTypeAll, domainIdAll);
				var noActions = NoActionTicketsForDailyTotalProfit2(now, gameTypeAll, domainIdAll);
				var dailyTotalProfitRecords = AccumulateDailyTotalProfitRecords2(winners, losers, noActions);
				SaveInDailyTotalProfitStorage(dailyTotalProfitRecords);
				return dailyTotalProfitRecords;
			}

			internal void AccumulateTotalProfitByDrawing(DateTime now)
			{
				if (!ExistsTable(TABLE_TOTAL_PROFIT_BY_DRAWING)) CreateTotalProfitByDrawingStorage();

				var allAccountNumber = ALL;
				var allGameType = ALL;
				var allTicketNumbers = ALL;
				var allDomainId = ALL;
                var allDrawingId = ALL;
                var winners = WinnerTicketsForDrawingsReport(now, allDrawingId, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
				var losers = LoserTicketsForDrawingsReport(now, allDrawingId, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
				var noActions = NoActionTicketsForDrawingsReport(now, allDrawingId, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
				var totalProfitByDrawingRecords = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);

				SaveInTotalProfitByDrawingStorage(totalProfitByDrawingRecords);
			}

			internal void AccumulateTotalProfitByDrawing(DateTime startDate, DateTime endDate, DateTime now)
			{
				var yesterday = now.AddDays(-1);
				endDate = new DateTime(Math.Min(yesterday.Ticks, endDate.Ticks));
				if (ExistsTable(TABLE_TOTAL_PROFIT_BY_DRAWING))
				{
					startDate = (LastDateInTotalProfitByDrawing()).AddDays(1);
				}
				else
				{
					CreateTotalProfitByDrawingStorage();
				}
				if (startDate.Date > endDate.Date) return;

				var allAccountNumber = ALL;
				var allGameType = ALL;
				var allTicketNumbers = ALL;
				var allDomainId = ALL;
                var allDrawingId = ALL;
                for (var currentDate = startDate.Date; currentDate.Date <= endDate.Date; currentDate = currentDate.AddDays(1))
				{
					var winners = WinnerTicketsForDrawingsReport(currentDate, currentDate, allDrawingId, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
					var losers = LoserTicketsForDrawingsReport(currentDate, currentDate, allDrawingId, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
					var noActions = NoActionTicketsForDrawingsReport(currentDate, currentDate, allDrawingId, allAccountNumber, allGameType, allTicketNumbers, allDomainId);
					var totalProfitByDrawingRecords = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);
					SaveInTotalProfitByDrawingStorage(totalProfitByDrawingRecords);
				}
			}

            protected const int AllCurrencies = -1;
            internal void UpdateDailyTotalProfit2(DateTime dateWithoutTime, string gameType)
			{
				if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException($"Day to update table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				var allDomain = ALL;
				var winners = WinnerTicketsForDailyTotalProfit2(dateWithoutTime, dateWithoutTime, gameType, allDomain, AllCurrencies);
				var losers = LoserTicketsForDailyTotalProfit2(dateWithoutTime, dateWithoutTime, gameType, allDomain, AllCurrencies);
				var dailyTotalProfitRecords2 = AccumulateDailyTotalProfitRecords2(winners, losers);

				RemoveInDailyTotalProfitStorage2(dateWithoutTime, gameType);
				SaveInDailyTotalProfitStorage(dailyTotalProfitRecords2);
			}

			internal DailyTotalProfitReport UpdateDailyTotalProfitAt(DateTime day)
			{
				if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException($"Day to update table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");

				var allGameType = ALL;
				var allDomain = ALL;

				var winners = WinnerTicketsForDailyTotalProfit2(day, day, allGameType, allDomain, AllCurrencies);
				var losers = LoserTicketsForDailyTotalProfit2(day, day, allGameType, allDomain, AllCurrencies);
				var dailyTotalProfitRecords2 = AccumulateDailyTotalProfitRecords2(winners, losers);

				RemoveInDailyTotalProfitStorageAt(day);
				SaveInDailyTotalProfitStorage(dailyTotalProfitRecords2);
				var report = new DailyTotalProfitReport(dailyTotalProfitRecords2, Enumerable.Empty<DailyTotalProfitRecord2>());
				return report;
			}

			internal void UpdateTotalProfitByDrawing(DateTime drawDate, string state, string gameType)
			{
				if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				var winners = WinnerTicketsForTotalProfitByDrawing(drawDate, gameType, state);
				var losers = LoserTicketsForTotalProfitByDrawing(drawDate, gameType, state);
				var noActions = NoActionTicketsForTotalProfitByDrawing(drawDate, gameType, state);
				var totalProfitByDrawingRecords = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);

				RemoveInTotalProfitByDrawingStorage(drawDate, state, gameType);
				SaveInTotalProfitByDrawingStorage(totalProfitByDrawingRecords);
			}

			internal TotalProfitByDrawingReport UpdateTotalProfitByDrawingAt(DateTime day)
			{
				if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_TOTAL_PROFIT_BY_DRAWING}' can not have hours or minutes.");

				var allGameType = string.Empty;
				var allStates = string.Empty;
				var winners = WinnerTicketsForTotalProfitByDrawingAt(day, allGameType, allStates);
				var losers = LoserTicketsForTotalProfitByDrawingAt(day, allGameType, allStates);
				var noActions = NoActionTicketsForTotalProfitByDrawingAt(day, allGameType, allStates);
				var totalProfitByDrawingRecords = AccumulateTotalProfitByDrawingRecords(winners, losers, noActions);

				RemoveInTotalProfitByDrawingStorageAt(day);
				SaveInTotalProfitByDrawingStorage(totalProfitByDrawingRecords);
				var report = new TotalProfitByDrawingReport(totalProfitByDrawingRecords);
				return report;
			}

			internal abstract DateTime LastDateInDailyTotalProfit(string table);
			internal abstract DateTime LastDateInDailyTotalProfit();
			internal abstract DateTime LastDateInTotalProfitByDrawing();

			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId);
			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime drawDate, string gameType, string domainIds);
			
			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state);
			protected abstract IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state);

			protected abstract IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId);
			protected abstract IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime drawDate, string gameType, string domainIds);
			
			protected abstract IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state);
			protected abstract IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state);

			protected abstract IEnumerable<WinnerInfo> NoActionTicketsForDailyTotalProfit2(DateTime drawDate, string gameType, string domainIds);
			
			protected abstract IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state);
			protected abstract IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state);

			protected abstract List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId);

			protected abstract List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string gameType, string drawingId, string domainIds);

			
			protected abstract void RemoveInDailyTotalProfitStorage(DateTime drawDate, string gameType);
			protected abstract void RemoveInDailyTotalProfitStorage2(DateTime drawDate, string gameType);
			protected abstract void RemoveInDailyTotalProfitStorageAt(DateTime day);
			protected abstract void RemoveInTotalProfitByDrawingStorage(DateTime drawDate, string state, string gameType);
			protected abstract void RemoveInTotalProfitByDrawingStorageAt(DateTime day);

			internal abstract IEnumerable<TicketRecord> GenerateMoneyInvestedReport(DateTime startDate, DateTime endDate);

			internal SalesAndPrizesPerDayReport GenerateSalesAndPrizesPerDayReport(DateTime startDate, DateTime endDate)
			{
				var winners = WinnerTicketsBetween(startDate, endDate);
				var losers = LoserTicketsBetween(startDate, endDate);
				var report = new SalesAndPrizesPerDayReport(startDate, endDate, winners, losers);
				return report;
			}

			internal CountsOfTicketsPerDayAndGameReport GenerateCountsOfTicketsPerDayAndGameReport(DateTime startDate, DateTime endDate)
			{
				var winners = WinnerTicketsBetween(startDate, endDate);
				var losers = LoserTicketsBetween(startDate, endDate);
				var report = new CountsOfTicketsPerDayAndGameReport(startDate, endDate, winners, losers);
				return report;
			}

			protected abstract IEnumerable<WinnerInfo> WinnerTicketsBetween(DateTime startDate, DateTime endDate);

			protected abstract IEnumerable<WinnerInfo> LoserTicketsBetween(DateTime startDate, DateTime endDate);
			
			
			protected abstract string DateTimeToString(DateTime date);

			protected CompletedPicksDraws GenerateRecordsForDrawingReport(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers, IEnumerable<WinnerInfo> noActions)
			{
				var completedDraws = new CompletedPicksDraws(winners, losers, noActions);
				return completedDraws;
			}

			protected TicketsPerPlayersInCompletedPicksDraws GenerateRecordsForTicketsPerPlayersInDrawingReport(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers, IEnumerable<WinnerInfo> noActions)
			{
				var completedDraws = new CompletedPicksDraws(winners, losers, noActions);
				var ticketsPerPlayers = new TicketsPerPlayersInCompletedPicksDraws(completedDraws);
				return ticketsPerPlayers;
			}

			protected WagersPerPlayerInCompletedDraw GenerateRecordsForWagersPerPlayerInDrawingReport(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers, IEnumerable<WinnerInfo> noActions, string fullTicketNumber)
			{
				var completedDraws = new CompletedPicksDraws(winners, losers, noActions);
				var ticketsPerPlayers = new TicketsPerPlayersInCompletedPicksDraws(completedDraws);
				var wagersPerPlayer = new WagersPerPlayerInCompletedDraw(ticketsPerPlayers, fullTicketNumber);
				return wagersPerPlayer;
			}

			protected IEnumerable<PickWinnerRecord> GenerateRecordsForWinnersReport(IEnumerable<WinnerInfo> winners)
			{
				var records = winners.SelectMany(x => x.WinnerWagers()).Select(x => new PickWinnerRecord
				{
					AccountNumber = x.WinnerInfo.AccountNumber,
					Date = x.WinnerInfo.DrawDate(),
					TicketNumber = x.Wager.FullWagerNumber(x.WinnerInfo.TicketNumber),
					TicketsCount = x.WinnerInfo.Wagers().Count(),
                    IdOfLottery = x.WinnerInfo.GetIdOfLottery(),
                    UniqueDrawingId = x.WinnerInfo.UniqueDrawingId,
					DrawingName = x.WinnerInfo.DrawingName,
					DomainUrl = x.WinnerInfo.DomainUrl,
					DomainId = x.WinnerInfo.DomainId,
                    AffiliateId = x.WinnerInfo.AffiliateId,
					GameType = x.WinnerInfo.IdOfLottery.ToString(),
					Amount = x.WinnerInfo.Amount,
					TicketCost = x.WinnerInfo.TicketAmount(),
					WinnerNumber = x.WinnerInfo.Draw,
                    WinnerDigits = x.WinnerInfo.WinnerDigits(),
                    Fireball = x.WinnerInfo.Fireball,
                    TypeNumberSequenceAsText = x.WinnerInfo.TypeNumberSequenceAsText(),
                    Prize = x.Prize,
					State = x.WinnerInfo.StateAbb,
                    CreationDate = x.WinnerInfo.Creation
                });
				return records;
			}

			internal List<DailyTotalProfitRecord2> AccumulateDailyTotalProfitRecords2(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers, IEnumerable<WinnerInfo> noActions = null)
			{
				var tickets = new SortedDictionary<DailyProfitKey, DailyTotalProfitRecord2>();
				foreach (var winner in winners)
				{
					var key = new DailyProfitKey(winner.DrawDate().Date, winner.IdOfLottery.ToString(), winner.DomainId, winner.AffiliateId, winner.CurrencyId);
                    var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new DailyTotalProfitRecord2(winner.DrawDate().Date, winner.IdOfLottery.ToString(), winner.DomainId, winner.AffiliateId, winner.DomainUrl, winner.CurrencyId);
						tickets.Add(key, value);
					}
					value.AddTicketInfo(winner, true);
				}

				foreach (var loser in losers)
				{
                    var key = new DailyProfitKey(loser.DrawDate().Date, loser.IdOfLottery.ToString(), loser.DomainId, loser.AffiliateId, loser.CurrencyId);
                    var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new DailyTotalProfitRecord2(loser.DrawDate().Date, loser.IdOfLottery.ToString(), loser.DomainId, loser.AffiliateId, loser.DomainUrl, loser.CurrencyId);
						tickets.Add(key, value);
					}
					value.AddTicketInfo(loser, false);
				}

				if (noActions != null)
				{
					foreach (var noAction in noActions)
					{
                        var key = new DailyProfitKey(noAction.DrawDate().Date, noAction.IdOfLottery.ToString(), noAction.DomainId, noAction.AffiliateId, noAction.CurrencyId);
                        var value = tickets.GetValueOrDefault(key);
						if (value == null)
						{
							value = new DailyTotalProfitRecord2(noAction.DrawDate().Date, noAction.IdOfLottery.ToString(), noAction.DomainId, noAction.AffiliateId, noAction.DomainUrl, noAction.CurrencyId);
							tickets.Add(key, value);
						}
						value.AddTicketNoActionInfo(noAction);
					}
				}

				return tickets.Values.ToList();
			}

			internal List<TotalProfitByDrawingRecord> AccumulateTotalProfitByDrawingRecords(IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers, IEnumerable<WinnerInfo> noActions)
			{
				var tickets = new SortedDictionary<string, TotalProfitByPicksDrawingRecord>();
				foreach (var winner in winners)
				{
					var key = $"{winner.DrawDate()}{winner.StateAbb}{winner.DrawingId}{winner.IdOfLottery}{winner.AffiliateId}{winner.DomainId}";
					var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new TotalProfitByPicksDrawingRecord(winner.DrawDate(), winner.StateAbb, winner.IdOfLottery.ToString(), winner.AffiliateId, winner.DomainId, winner.DomainUrl, winner.DrawingId, winner.Position, winner.DrawingName);
						tickets.Add(key, value);
					}
					value.AddTicketInfo(winner, true);
				}

				foreach (var loser in losers)
				{
					var key = $"{loser.DrawDate()}{loser.StateAbb}{loser.DrawingId}{loser.IdOfLottery}{loser.AffiliateId}{loser.DomainId}";
					var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new TotalProfitByPicksDrawingRecord(loser.DrawDate(), loser.StateAbb, loser.IdOfLottery.ToString(), loser.AffiliateId, loser.DomainId, loser.DomainUrl, loser.DrawingId, loser.Position, loser.DrawingName);
						tickets.Add(key, value);
					}
					value.AddTicketInfo(loser, false);
				}

				foreach (var noAction in noActions)
				{
					var key = $"{noAction.DrawDate()}{noAction.StateAbb}{noAction.DrawingId}{noAction.IdOfLottery}{noAction.AffiliateId}{noAction.DomainId}";
					var value = tickets.GetValueOrDefault(key);
					if (value == null)
					{
						value = new TotalProfitByPicksDrawingRecord(noAction.DrawDate(), noAction.StateAbb, noAction.IdOfLottery.ToString(), noAction.AffiliateId, noAction.DomainId, noAction.DomainUrl, noAction.DrawingId, noAction.Position, noAction.DrawingName);
						tickets.Add(key, value);
					}
					value.AddTicketNoActionInfo(noAction);
				}

				return tickets.Values.Cast<TotalProfitByDrawingRecord>().ToList();
			}
			
			protected IEnumerable<TicketRecord> GenerateRecordsForMoneyInvestedReport(IEnumerable<TicketRecord> tickets, DateTime startDate, DateTime endDate)
			{
				List<string> gameTypes = new List<string>() { ID_PICK2, ID_PICK3, ID_PICK4, ID_PICK5, ID_POWERBALL };
				List<Tuple<DateTime, DateTime>> weekList = ListOfWeeks(startDate, endDate);

				Func<DateTime, int> weekProjector = d => CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(d, CalendarWeekRule.FirstFourDayWeek, DayOfWeek.Sunday);

				var moneyByGameType = from ticket in tickets.OrderBy(x => x.Date)
									  group ticket by new { ticket.GameType, ticket.Date } into ticketGroup
									  select new TicketRecord(ticketGroup.Sum(x => x.Prize), 0, ticketGroup.Sum(x => x.Amount), ticketGroup.First().GameType, ticketGroup.First().Date);

				bool notExistMoneyInvestedRecordForGameType = false;
				foreach (string gameType in gameTypes)
				{
					notExistMoneyInvestedRecordForGameType = moneyByGameType.FirstOrDefault(x => x.GameType == gameType) == null;
					if (notExistMoneyInvestedRecordForGameType)
					{
						moneyByGameType = moneyByGameType.Append(new TicketRecord(0, 0, 0, gameType, new DateTime()));
					}
				}

				moneyByGameType = moneyByGameType.OrderBy(x => x.GameType);
				return moneyByGameType;
			}

			private List<Tuple<DateTime, DateTime>> ListOfWeeks(DateTime startDate, DateTime endDate)
			{
				List<Tuple<DateTime, DateTime>> weekList = new List<Tuple<DateTime, DateTime>>();

				for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
				{
					Tuple<DateTime, DateTime> firsLastDayOfWeek = GetFirstAndLastWeekDate(date);
					if (!weekList.Contains(firsLastDayOfWeek))
					{
						weekList.Add(firsLastDayOfWeek);
					}
				}
				return weekList;
			}

			private static Tuple<DateTime, DateTime> GetFirstAndLastWeekDate(DateTime date, DayOfWeek firstDayOfWeek = DayOfWeek.Sunday)
			{
				int diff = (7 + (date.DayOfWeek - firstDayOfWeek)) % 7;
				DateTime firstDay = date.AddDays(-1 * diff).Date;
				DateTime lastDay = firstDay.AddDays(6);
				if (date.Month != firstDay.Month)
				{
					firstDay = new DateTime(date.Year, date.Month, 1);
				}
				if (date.Month != lastDay.Month)
				{
					lastDay = new DateTime(date.Year, date.Month, 1).AddMonths(1).AddSeconds(-1);
				}
				return Tuple.Create(firstDay, lastDay);
			}

			internal abstract List<AffiliateData> ListAffiliates();

			internal DateTime LastDateInDailyTotalProfit2()
			{
				return LastDateInDailyTotalProfit(TABLE_DAILY_TOTAL_PROFIT2);
			}

			internal bool ExistsDailyTotalProfitStorage2()
			{
				return ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
			}
		}

		private class LottoDBHandlerMySQL : LottoDBHandlerPicks
		{
			private const string COMMON_JOIN_FOR_TICKETS = @"INNER JOIN l900drawings DR ON T.DRAWINGID = DR.ID
                                                            INNER JOIN l900domains DO ON T.DOMAINID = DO.ID ";
			
			internal LottoDBHandlerMySQL(string connectionString) : base(connectionString)
			{
				if (!ExistTable()) throw new GameEngineException($"There is no table {TABLE_LOOSERS}.");
                bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
                if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();
            }

			internal LottoDBHandlerMySQL(Company company, string connectionString) : base(company, connectionString)
			{
				if (!ExistTable()) throw new GameEngineException($"There is no table {TABLE_LOOSERS}.");
                bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
                if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();
            }

			private bool ExistTable()
			{
				bool exists = true;
				string sql = "SELECT 1 FROM " + TABLE_LOOSERS + " LIMIT 1";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand command = new MySqlCommand(sql, connection))
					{
						try
						{
							var dataReader = command.ExecuteReader();
							dataReader.Close();
						}
						catch
						{
							exists = false;
						}
					}
					connection.Close();
				}
				return exists;
			}

			protected override string DateTimeToString(DateTime date)
			{
				string result = date.ToString("yyyy-MM-dd HH:mm:ss");
				return result;
			}

			private string DrawingName(string command)
			{
				string drawingName = "";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								drawingName = reader.GetString(0);
							}
						}
					}
					connection.Close();
				}
				return drawingName;
			}

			private IEnumerable<WinnerInfo> WinnerTickets(string command)
			{
				DateTime tempDate;
				List<WinnerInfo> winnerList = new List<WinnerInfo>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							WinnerInfo info;
                            while (reader.Read())
                            {
                                tempDate = reader.GetDateTime(1);
                                info = new WinnerInfo(
                                    company: this.Company,
                                    draw: reader.GetString(6),
                                    fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                    gradedBy: reader.GetString(9),
                                    prize: reader.GetDecimal(17),
                                    stateAbb: reader.GetString(0),
                                    hour: tempDate.Hour,
                                    minute: tempDate.Minute,
                                    year: tempDate.Year,
                                    month: tempDate.Month,
                                    day: tempDate.Day,
                                    accountNumber: reader.GetString(2),
                                    ticket: reader.GetString(3),
                                    countOfTickets: reader.GetInt16(4),
                                    amount: reader.GetDecimal(5),
                                    selectionMode: (Ticket.Selection)reader.GetByte(8),
                                    action: (GameboardStatus)reader.GetByte(10),
                                    drawingId: reader.GetInt32(19),
									uniqueDrawingId: 0,
                                    position: (TypeNumberSequence)reader.GetByte(20),
                                    drawingName: reader.GetString(11),
                                    creation: reader.GetDateTime(12),
                                    orderNumber: reader.GetInt32(13),
                                    ticketNumber: reader.GetInt32(14),
                                    subticketsAndWagerNumbers: reader.GetString(15),
                                    profit: reader.GetDecimal(16),
                                    prizesVersion: reader.GetInt32(18),
                                    domainId: reader.GetInt16(21),
                                    domainUrl: reader.GetString(22),
                                    currencyId: reader.GetInt32(23)
                                );

                                winnerList.Add(info);
                            }
						}
					}
					connection.Close();
				}
				return winnerList;
			}

            private WinnerOrLooserRows WinnerTicketsAfterProcedures(string command, string startDateTimeAsText, string endDateTimeAsText)
            {
                DateTime tempDate;
                WinnerOrLooserRows winners = new WinnerOrLooserRows();

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();

                    MySqlCommand cmd1 = new MySqlCommand("SearchMinLoosersIdUsingIdx", connection);
                    cmd1.CommandType = CommandType.StoredProcedure;
                    cmd1.Parameters.AddWithValue("@search_date", startDateTimeAsText);
                    cmd1.Parameters.Add("@result_id", MySqlDbType.Int32).Direction = ParameterDirection.Output;
                    cmd1.ExecuteNonQuery();
                    object result1 = cmd1.Parameters["@result_id"].Value;
                    int firstId = result1 == DBNull.Value ? 0 : Convert.ToInt32(result1);

                    MySqlCommand cmd2 = new MySqlCommand("SearchMinLoosersIdUsingIdx", connection);
                    cmd2.CommandType = CommandType.StoredProcedure;
                    cmd2.Parameters.AddWithValue("@search_date", endDateTimeAsText);
                    cmd2.Parameters.Add("@result_id", MySqlDbType.Int32).Direction = ParameterDirection.Output;
                    cmd2.ExecuteNonQuery();
                    object result2 = cmd2.Parameters["@result_id"].Value;
                    int lastId = result2 == DBNull.Value ? 0 : Convert.ToInt32(result2);

                    using (MySqlCommand cmd = new MySqlCommand(command, connection))
                    {
                        cmd.Parameters.AddWithValue("@firstId", firstId);
                        cmd.Parameters.AddWithValue("@lastId", lastId);
                        using (DbDataReader reader = cmd.ExecuteReader())
                        {
                            WinnerOrLooserRow info;
                            while (reader.Read())
                            {
                                string accountNumber = reader.GetString(2);
                                int affiliateId = reader.GetInt32(22);

                                AffiliateWithAccount account = winners.AddAccount(accountNumber, affiliateId);

                                tempDate = reader.GetDateTime(1);
                                info = new WinnerOrLooserRow(
                                    stateAbb: reader.GetString(0),
                                    day: tempDate.Day,
                                    hour: tempDate.Hour,
                                    minute: tempDate.Minute,
                                    month: tempDate.Month,
                                    year: tempDate.Year,
                                    ticket: reader.GetString(3),
                                    countOfTickets: reader.GetInt16(4),
                                    amount: reader.GetDecimal(5),
                                    draw: reader.GetString(6),
                                    fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                    selectionMode: (Ticket.Selection)reader.GetByte(8),
                                    gradedBy: reader.GetString(9),
                                    action: (GameboardStatus)reader.GetByte(10),
                                    drawingId: reader.GetInt32(19),
                                    uniqueDrawingId: reader.GetInt32(20),
                                    position: (TypeNumberSequence)reader.GetByte(21),
                                    drawingName: reader.GetString(11),
                                    creation: reader.GetDateTime(12),
                                    orderNumber: reader.GetInt32(13),
                                    ticketNumber: reader.GetInt32(14),
                                    subticketsAndWagerNumbers: reader.GetString(15),
                                    profit: reader.GetDecimal(16),
                                    prize: reader.GetDecimal(17),
                                    account: account,
                                    domainId: reader.GetInt16(23),
                                    domainUrl: reader.GetString(24),
                                    currencyId: reader.GetInt32(25)
                                );

                                winners.Add(info);
                            }
                        }
                    }
                    connection.Close();
                }
                return winners;
            }

			private WinnerOrLooserRows WinnerTickets2(string command)
			{
				DateTime tempDate;
				WinnerOrLooserRows winners = new WinnerOrLooserRows();

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();

					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (DbDataReader reader = cmd.ExecuteReader())
						{
							WinnerOrLooserRow info;
							while (reader.Read())
							{
								string accountNumber = reader.GetString(2);
								int affiliateId = reader.GetInt32(22);

								AffiliateWithAccount account = winners.AddAccount(accountNumber, affiliateId);

								tempDate = reader.GetDateTime(1);
								info = new WinnerOrLooserRow(
									stateAbb: reader.GetString(0),
									day: tempDate.Day,
									hour: tempDate.Hour,
									minute: tempDate.Minute,
									month: tempDate.Month,
									year: tempDate.Year,
									ticket: reader.GetString(3),
									countOfTickets: reader.GetInt16(4),
									amount: reader.GetDecimal(5),
									draw: reader.GetString(6),
									fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                    selectionMode: (Ticket.Selection)reader.GetByte(8),
									gradedBy: reader.GetString(9),
									action: (GameboardStatus)reader.GetByte(10),
									drawingId: reader.GetInt32(19),
                                    uniqueDrawingId: reader.GetInt32(20),
									position: (TypeNumberSequence)reader.GetByte(21),
                                    drawingName: reader.GetString(11),
									creation: reader.GetDateTime(12),
                                    orderNumber: reader.GetInt32(13),
                                    ticketNumber: reader.GetInt32(14),
									subticketsAndWagerNumbers: reader.GetString(15),
									profit: reader.GetDecimal(16),
									prize: reader.GetDecimal(17),
									account: account,
									domainId: reader.GetInt16(23),
									domainUrl: reader.GetString(24),
                                    currencyId: reader.GetInt32(25)
                                );

								winners.Add(info);
							}
						}
					}
					connection.Close();
				}
				return winners;
			}

			
			private IEnumerable<LoserInfo> LoserTickets(string command)
			{
				DateTime tempDate;
				List<LoserInfo> looserList = new List<LoserInfo>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						try
						{
                            using (MySqlDataReader reader = cmd.ExecuteReader())
                            {
                                LoserInfo info;
                                while (reader.Read())
                                {
                                    tempDate = reader.GetDateTime(1);
                                    info = new LoserInfo(
                                        company: this.Company,
                                        draw: reader.GetString(6),
                                        fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                        gradedBy: reader.GetString(9),
                                        stateAbb: reader.GetString(0),
                                        hour: tempDate.Hour,
                                        minute: tempDate.Minute,
                                        year: tempDate.Year,
                                        month: tempDate.Month,
                                        day: tempDate.Day,
                                        accountNumber: reader.GetString(2),
                                        ticket: reader.GetString(3),
                                        countOfTickets: reader.GetInt16(4),
                                        amount: reader.GetDecimal(5),
                                        selectionMode: (Ticket.Selection)reader.GetByte(8),
                                        action: (GameboardStatus)reader.GetByte(10),
                                        drawingId: reader.GetInt32(18),
                                        position: (TypeNumberSequence)reader.GetByte(19),
                                        drawingName: reader.GetString(11),
                                        creation: reader.GetDateTime(12),
                                        orderNumber: reader.GetInt32(13),
                                        ticketNumber: reader.GetInt32(14),
                                        subticketsAndWagerNumbers: reader.GetString(15),
                                        profit: reader.GetDecimal(16),
                                        prizesVersion: reader.GetInt32(17),
                                        domainId: reader.GetInt16(20),
                                        domainUrl: reader.GetString(21),
                                        currencyId: reader.GetInt32(22)
                                    );

                                    looserList.Add(info);
                                }
                            }
                        }
                        catch (MySqlException e)
                        {
                            Loggers.GetIntance().Db.Error($"MySQL Execution Error in LoserTickets. SQL Query: [{command}]. MySQL Error Code: {e.Number}. Exception: {e.ToString()}", e);
                            throw;
                        }
                        catch (Exception e)
                        {
                            Loggers.GetIntance().Db.Error($"Generic Execution Error in LoserTickets. SQL Query: [{command}]. Exception: {e.ToString()}", e);
                            throw;
                        }
                    }
					connection.Close();
				}
				return looserList;
			}

			private IEnumerable<NoActionInfo> NoActionTickets(string command)
			{
				DateTime tempDate;
				List<NoActionInfo> noActionList = new List<NoActionInfo>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							NoActionInfo info;
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(1);
								info = new NoActionInfo(
									company: this.Company,
									noActionBy: reader.GetString(13),
									stateAbb: reader.GetString(0),
									hour: tempDate.Hour,
									minute: tempDate.Minute,
									year: tempDate.Year,
									month: tempDate.Month,
									day: tempDate.Day,
									accountNumber: reader.GetString(2),
									ticket: reader.GetString(3),
									countOfTickets: reader.GetInt16(4),
									amount: reader.GetDecimal(5),
									selectionMode: (Ticket.Selection)reader.GetByte(6),
									action: (GameboardStatus)reader.GetByte(7),
									drawingId: reader.GetInt32(15),
                                    position: (TypeNumberSequence)reader.GetByte(16),
                                    drawingName: reader.GetString(8),
									creation: reader.GetDateTime(9),
                                    orderNumber: reader.GetInt32(10),
                                    ticketNumber: reader.GetInt32(11),
									subticketsAndWagerNumbers: reader.GetString(12),
									prizesVersion: reader.GetInt32(14),
									domainId: reader.GetInt16(17),
									domainUrl: reader.GetString(18),
                                    currencyId: reader.GetInt32(19)
                                );

								noActionList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return noActionList;
			}

			private List<DailyTotalProfitRecord2> DailyTotalProfitRecords2(string command)
			{
				var records = new List<DailyTotalProfitRecord2>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (DbDataReader reader = cmd.ExecuteReader())
						{
							DailyTotalProfitRecord2 info;
							while (reader.Read())
							{
								info = new DailyTotalProfitRecord2(
									date: reader.GetDateTime(0),
									gameType: reader.GetString(1),
									domainId: reader.GetInt16(7),
									affiliateId: reader.GetInt32(9),
                                    currencyId: reader.GetInt32(10),
									ticketsCount: reader.GetInt32(2),
									winnersCount: reader.GetInt32(3),
									sold: reader.GetDecimal(4),
									prizes: reader.GetDecimal(5),
									profits: reader.GetDecimal(6),
									domainUrl: reader.GetString(8)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command)
			{
				var records = new List<TotalProfitByDrawingRecord>();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							TotalProfitByPicksDrawingRecord info;
							while (reader.Read())
							{
								info = new TotalProfitByPicksDrawingRecord(
									date: reader.GetDateTime(0),
									state: reader.GetString(1),
									gameType: reader.GetString(2),
									ticketsCount: reader.GetInt32(3),
									playersCount: reader.GetInt32(4),
									sold: reader.GetDecimal(5),
									prizes: reader.GetDecimal(6),
									profits: reader.GetDecimal(7),
									domainId: reader.GetInt32(8),
									domainUrl: reader.GetString(9),
									affiliateId: reader.GetInt32(10),
									drawingId: reader.GetInt32(11),
									position: (TypeNumberSequence)reader.GetByte(12),
                                    drawingName: reader.GetString(13)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override CompletedLastPicksDraws GetDrawings(string command, string accountNumber)
			{
				var result = new CompletedLastPicksDraws();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
                        cmd.Parameters.AddWithValue("@accountNumber", accountNumber);
                        using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							CompletedPicksDraw info;
							while (reader.Read())
							{
								info = new CompletedPicksDraw(
									drawDate: reader.GetDateTime(0),
									state: reader.GetString(1)
								);
								var key = new CompletedDrawKey(info.DrawDate, info.State, string.Empty);
								result.Add(key, info);
							}
						}
					}
					connection.Close();
				}
				return result;
			}

			private IEnumerable<TicketRecord> Tickets(string command)
			{
				List<TicketRecord> ticketList = new List<TicketRecord>();

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					connection.Open();
					using (MySqlCommand cmd = new MySqlCommand(command, connection))
					{
						using (MySqlDataReader reader = cmd.ExecuteReader())
						{
							TicketRecord info;
							while (reader.Read())
							{
								info = new TicketRecord(reader.GetDecimal(0), reader.GetInt32(1), reader.GetDecimal(2), reader.GetString(3), reader.GetDateTime(4));
								ticketList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return ticketList;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where DATE(date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where DATE(date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			private string DateSeparatedWithORs(List<DateTime> dates)
			{
				var linesForDate = new List<string>();
				foreach (var date in dates)
				{
					linesForDate.Add($"DATE(date)='{DateToString(date)}'");
				}
				string dateSeparatedWithORs = "(" + string.Join(" or ", linesForDate) + ")";
				return dateSeparatedWithORs;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where DATE(date)<='{DateToString(endedDate)}' and DATE(date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where DATE(date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where DATE(date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where DATE(date)<='{DateToString(endedDate)}' and DATE(date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> GetPlayedTicketsBy(string ticketNumber)
			{
				var command = $@"
					({COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
                    where ticketnumber='{ticketNumber.ToUpper()}' and timestamp = 0
                    union
					select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
                    where ticketnumber='{ticketNumber.ToUpper()}' and timestamp = 0
                    union
					select state, date, account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where ticketnumber='{ticketNumber.ToUpper()}' and timestamp = 0)
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override WinnerInfo GetWinnerTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where state='{state}' and creation='{DateTimeToString(creationDate)}' and DATE(date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result.FirstOrDefault();
			}

			internal override WinnerInfo WinnerTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where state='{state}' and DATE(date)='{DateToString(drawDate)}' and ticketnumber='{ticketNumber.ToUpper()}' 
						and timestamp = 0
                    order by state, date;";
				var result = WinnerTickets(command);
				return result.FirstOrDefault();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where DATE(date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where DATE(date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}  
                    where DATE(date)<='{DateToString(endedDate)}' and DATE(date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS} 
                    where DATE(date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS} 
                    where DATE(date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS} 
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS} 
                    where DATE(date)<='{DateToString(endedDate)}' and DATE(date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override LoserInfo GetLoserTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and creation='{DateTimeToString(creationDate)}' and DATE(date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result.FirstOrDefault();
			}

			internal override LoserInfo LoserTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and DATE(date)='{DateToString(drawDate)}' and account='{ticketNumber.ToUpper()}' 
						and timestamp = 0
                    order by state, date;";
				var result = LoserTickets(command);
				return result.FirstOrDefault();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)<='{DateToString(endedDate)}' and DATE(date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)<='{DateToString(endedDate)}' and DATE(date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override NoActionInfo GetNoActionTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and creation='{DateTimeToString(creationDate)}' and DATE(date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result.FirstOrDefault();
			}

			internal override NoActionInfo NoActionTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and DATE(date)='{DateToString(drawDate)}' and ticketNumber='{ticketNumber.ToUpper()}' 
						and timestamp = 0
                    order by state, date;";
				var result = NoActionTickets(command);
				return result.FirstOrDefault();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where date<'{DateToString(endDate.AddDays(1))}' and date>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS} 
                    where date<'{DateToString(endDate.AddDays(1))}' and date>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' and timestamp = 0;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where date<'{DateToString(endDate.AddDays(1))}' and date>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' and timestamp = 0
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where date='{DateTimeToString(drawDate)}' and state='{state.ToUpper()}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS} 
                    where date='{DateTimeToString(drawDate)}' and state='{state.ToUpper()}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' and state='{state.ToUpper()}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> TopWinnersForDrawing(DateTime drawDate, string state, string gameType, string domainUrl)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where date='{DateTimeToString(drawDate)}' and state='{state.ToUpper()}' and left(ticket,2) ='{gameType}' and url='{domainUrl}' 
						and timestamp = 0
                    order by prize desc;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $"T.account='{accountNumber.ToUpper()}' and ";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL((SELECT affiliateid FROM {TABLE_USER_PER_AFFILIATE} WHERE account = T.account LIMIT 1), {AffiliateData.DefaultAffiliateId}) as affiliateid, domainid, url, currencyid
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
                    where {accountNumberFilter} date>='{DateTimeToString(startDate)}' and date<'{DateTimeToString(endDate.AddDays(1))}' 
						{ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter} and timestamp = 0 ";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(drawDate)}'  
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			internal override CompletedPicksDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
				var report = GenerateRecordsForDrawingReport(winners, losers, noActions);
				return report;
			}

			internal override TicketsPerPlayersInCompletedPicksDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
				var report = GenerateRecordsForTicketsPerPlayersInDrawingReport(winners, losers, noActions);
				return report;
			}

			internal override WagersPerPlayerInCompletedDraw GenerateWagersPerPlayerInDrawingReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string fullTicketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, fullTicketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, fullTicketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, uniqueDrawingId, accountNumber, gameType, fullTicketNumber, domainIds);
				var report = GenerateRecordsForWagersPerPlayerInDrawingReport(winners, losers, noActions, fullTicketNumber);
				return report;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? "" : $" and T.account='{accountNumber.ToUpper()}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' {accountNumberFilter} {domainIdFilter}
						and timestamp = 0 
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			private IEnumerable<WinnerInfo> WinnerTicketsForWinnersOfTheMonthReport(DateTime date)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
                    where MONTH(date) = {date.Month}
						and timestamp = 0 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			private string DrawingNameForTopWinnersReport(int drawId)
			{
				var command = $@"select drawingname
                    from {TABLE_DRAWINGS} DR
                    where ID = {drawId};";
				var name = DrawingName(command);
				return name;
			}

			internal override IEnumerable<PickWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var winners = WinnerTicketsForWinnersReport(startDate, endDate, accountNumber, domainIds);
				var records = GenerateRecordsForWinnersReport(winners);
				return records.ToList();
			}

			public override IEnumerable<PickWinnerRecord> GenerateWinnersOfTheMonthReport(DateTime date)
			{
				var winners = WinnerTicketsForWinnersOfTheMonthReport(date);
				var records = GenerateRecordsForWinnersReport(winners);
				return records.ToList();
			}

			public override string DrawingNameFor(int drawId)
			{
				var name = DrawingNameForTopWinnersReport(drawId);
				return name;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var currencyIdFilter = string.Empty;
                if (!Enum.TryParse(Company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {Company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(Company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {Company.Lotto900().RewardCurrency} does not exist");
                if (currencyId == (int)currency)
                    currencyIdFilter = $" and (currencyid = {currencyId} or currencyid ={(int)rewardCurrency})";
                else if (currencyId != AllCurrencies)
                    currencyIdFilter = $" and currencyid = {currencyId}";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS} 
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}' {gameTypeFilter} {domainIdFilter} {currencyIdFilter}
						and timestamp = 0 
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS} 
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where DATE(date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0 
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS} 
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(day)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var currencyIdFilter = string.Empty;
                if (!Enum.TryParse(Company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {Company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(Company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {Company.Lotto900().RewardCurrency} does not exist");
                if (currencyId == (int)currency)
                    currencyIdFilter = $" and currencyid IN ({currencyId},{(int)rewardCurrency})";
                else if (currencyId != AllCurrencies)
                    currencyIdFilter = $" and currencyid = {currencyId}";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL((SELECT affiliateid FROM {TABLE_USER_PER_AFFILIATE} WHERE account = T.account LIMIT 1), {AffiliateData.DefaultAffiliateId}) as affiliateid, domainid, url, currencyid
					from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
					where T.id >= @firstId AND T.id < @lastId {gameTypeFilter} {domainIdFilter} {currencyIdFilter} and T.date >= '{DateTimeToString(startDate.Date)}' and T.date < '{DateTimeToString(endDate.AddDays(1))}'
						and timestamp = 0";
				var winners = WinnerTicketsAfterProcedures(command, DateTimeToString(startDate.Date), DateTimeToString(endDate.AddDays(4)));
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL((SELECT affiliateid FROM {TABLE_USER_PER_AFFILIATE} WHERE account = T.account LIMIT 1), {AffiliateData.DefaultAffiliateId}) as affiliateid, domainid, url, currencyid
					from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
					where T.id >= @firstId AND T.id < @lastId {gameTypeFilter} {domainIdFilter} and T.date >= '{DateTimeToString(startDate.Date)}' and T.date < '{DateTimeToString(startDate.AddDays(1))}'
						and timestamp = 0";
				var winners = WinnerTicketsAfterProcedures(command, DateTimeToString(startDate.Date), DateTimeToString(startDate.AddDays(4)));
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var command = $@"select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where DATE(date)='{DateToString(day)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0 
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where DATE(date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var command = $@"select state, date, account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"select state, date, T.account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, IFNULL(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_NOACTIONS} T {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where date='{DateToString(day)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0 
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and gametype ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var currencyIdFilter = string.Empty;
                if (!Enum.TryParse(Company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {Company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(Company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {Company.Lotto900().RewardCurrency} does not exist");
                if (currencyId == (int)currency)
                    currencyIdFilter = $" and (currencyid = {currencyId} or currencyid ={(int)rewardCurrency})";
                else if (currencyId != AllCurrencies)
                    currencyIdFilter = $" and currencyid = {currencyId}";

                var command = $@"select date, gametype, ticketscount, winnerscount, sold, prizes, profits, domainid, url, affiliateid, currencyid
					from {TABLE_DAILY_TOTAL_PROFIT2} T INNER JOIN {TABLE_DOMAINS} DO ON T.DOMAINID = DO.ID
					where date>='{DateTimeToString(startDate)}' and date<'{DateTimeToString(endDate.AddDays(1))}' {currencyIdFilter} {domainIdFilter} {gameTypeFilter}
					order by date;";
				var result = DailyTotalProfitRecords2(command);
				return result;
			}

			protected override List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string gameType, string uniqueDrawingId, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and gametype ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
				var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

				var command = $@"select date, state, gametype, ticketscount, playerscount, sold, prizes, profits, domainid, url, affiliateid, DR.uniqueid, DR.position, drawingname
                    from {TABLE_TOTAL_PROFIT_BY_DRAWING} T {COMMON_JOIN_FOR_TICKETS}
                    where date>='{DateTimeToString(startDate)}' and date<'{DateTimeToString(endDate.AddDays(1))}' 
                        {drawingIdFilter} {gameTypeFilter} {domainIdFilter} 
                    order by date;";

				var result = TotalProfitByDrawingRecords(command);
				return result;
			}

			internal override bool ExistsTable(string table)
			{
				bool exists = true;
				string sql = $"SELECT 1 FROM { table } LIMIT 1";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();
							dataReader.Close();
						}
					}
					catch
					{
						exists = false;
					}
					finally
					{
						connection.Close();
					}
				}
				return exists;
			}

			internal override DateTime LastDateInDailyTotalProfit(string table)
			{
				if (!ExistsTable(table)) throw new GameEngineException($"There is no table {table} to get date");

                DateTime lastDate = default;
                var sql = $"SELECT MAX(DATE) FROM {table};";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
                            var result = command.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
							{
                                lastDate = (DateTime)result;
							}
						}
					}
					catch (Exception e)
					{
                        throw new GameEngineException($"MySQL Error [{sql}]. {e.Message}");
					}
					}

				return lastDate;
			}

			internal override DateTime LastDateInDailyTotalProfit()
			{
				if (!ExistsTable(TABLE_DAILY_TOTAL_PROFIT2)) throw new GameEngineException($"There is no table {TABLE_DAILY_TOTAL_PROFIT2} to get date");

                DateTime lastDate = default;
                var sql = $"SELECT MAX(DATE) FROM {TABLE_DAILY_TOTAL_PROFIT2};";

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
                            var result = command.ExecuteScalar();
                            if (result != null && result != DBNull.Value)
							{
                                lastDate = (DateTime)result;
							}
						}
					}
					catch (Exception e)
					{
                        throw new GameEngineException($"MySQL Error [{sql}]. {e.Message}");
					}
					}

				return lastDate;
			}

			internal override DateTime LastDateInTotalProfitByDrawing()
			{
				if (!ExistsTable(TABLE_TOTAL_PROFIT_BY_DRAWING)) throw new GameEngineException($"There is no table {TABLE_TOTAL_PROFIT_BY_DRAWING} to get date");
				var lastDate = new DateTime();
				var sql = $"SELECT DATE FROM {TABLE_TOTAL_PROFIT_BY_DRAWING} ORDER BY DATE DESC LIMIT 1;";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						using (DbDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								lastDate = reader.GetDateTime(0);
							}
							reader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
				return lastDate;
			}

			protected override void CreateDailyTotalProfitStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement
					.AppendLine($"CREATE TABLE IF NOT EXISTS {TABLE_DAILY_TOTAL_PROFIT2}")
					.AppendLine("(")
					.AppendLine("DATE DATETIME NOT NULL,")
					.AppendLine("GAMETYPE CHAR(2) NOT NULL,")
					.AppendLine("AFFILIATEID INT NOT NULL,")
					.AppendLine("TICKETSCOUNT MEDIUMINT UNSIGNED NOT NULL,")
					.AppendLine("WINNERSCOUNT SMALLINT UNSIGNED NOT NULL,")
					.AppendLine("SOLD DECIMAL(10,2) UNSIGNED NOT NULL,")
					.AppendLine("PRIZES DECIMAL(10,2) UNSIGNED NOT NULL,")
					.AppendLine("PROFITS DECIMAL(10,2) NOT NULL,")
					.AppendLine("DOMAINID SMALLINT UNSIGNED NOT NULL DEFAULT 0,")
                    .AppendLine("CURRENCYID INT UNSIGNED NOT NULL DEFAULT 2,")
                    .AppendLine("PRIMARY KEY (DATE, CURRENCYID, DOMAINID, GAMETYPE, AFFILIATEID)")
					.AppendLine(");");

				statement
					.AppendLine($"CREATE TABLE IF NOT EXISTS {TABLE_USER_PER_AFFILIATE}")
					.AppendLine("(")
					.AppendLine($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
					.AppendLine("AFFILIATEID INT NOT NULL,")
					.AppendLine("PRIMARY KEY (ACCOUNT)")
					.AppendLine(");");

				statement
					.AppendLine($"CREATE TABLE IF NOT EXISTS {TABLE_AFFILIATES}")
					.AppendLine("(")
					.AppendLine("ID INT NOT NULL,")
					.AppendLine("NAME VARCHAR(30) NOT NULL,")
					.AppendLine("PRIMARY KEY (ID)")
					.AppendLine(");");

				string sql = statement.ToString();
				ExecuteCommand(sql);
			}

			protected override void ExecuteCommand(string cmdText)
			{
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(cmdText, connection))
							{
								command.ExecuteNonQuery();
							}
						}
						catch (Exception e)
						{
							Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
							throw new GameEngineException("MySQL Error [" + cmdText + "]");
						}
						finally
						{
							connection.Close();
						}

						WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "UnknownAPI", "Lotto");
					}
					catch (Exception webhookEx)
					{
						Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
						throw;
					}
				}
			}

			protected override void CreateTotalProfitByDrawingStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement
					.AppendLine($"CREATE TABLE ").Append(TABLE_TOTAL_PROFIT_BY_DRAWING)
					.AppendLine("(")
					.AppendLine("DATE DATETIME NOT NULL,")
					.AppendLine("STATE CHAR(2) NOT NULL,")
					.AppendLine("GAMETYPE CHAR(2) NOT NULL,")
					.AppendLine("DRAWINGID INT UNSIGNED NOT NULL,")
					.AppendLine("TICKETSCOUNT MEDIUMINT UNSIGNED NOT NULL,")
					.AppendLine("PLAYERSCOUNT MEDIUMINT UNSIGNED NOT NULL,")
					.AppendLine("SOLD DECIMAL(10,2) UNSIGNED NOT NULL,")
					.AppendLine("PRIZES DECIMAL(10,2) UNSIGNED NOT NULL,")
					.AppendLine("PROFITS DECIMAL(10,2) NOT NULL,")
					.AppendLine("AFFILIATEID INT NOT NULL DEFAULT 0,")
					.AppendLine("DOMAINID SMALLINT UNSIGNED NOT NULL DEFAULT 0,")
					.AppendLine("INDEX IDX_DATE_DRAWINGID (DATE, DRAWINGID)")
					.AppendLine(");");

				string sql = statement.ToString();
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void SaveInDailyTotalProfitStorage(List<DailyTotalProfitRecord2> dailyTotalProfitRecords)
			{
				StringBuilder sql = new StringBuilder();

				if (dailyTotalProfitRecords.Count > 0)
				{
					foreach (var record in dailyTotalProfitRecords)
					{
						string recordToInsert = $@"INSERT INTO { TABLE_DAILY_TOTAL_PROFIT2 } (DATE, GAMETYPE, TICKETSCOUNT, WINNERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID, CURRENCYID, AFFILIATEID) VALUES
							('{ ToDateString(record.Date.Year, record.Date.Month, record.Date.Day, 0, 0, 0)}',
							'{ record.GameType}',
							'{ record.TicketsCount}',
							'{ record.WinnersCount}',
							'{ record.Sold}',
							'{ record.Prizes}',
							'{ record.Profits}',
							'{ record.DomainId}',
                            '{record.CurrencyId}',
							{ record.AffiliateId}
						) ON DUPLICATE KEY UPDATE
							TICKETSCOUNT = '{ record.TicketsCount}',
							WINNERSCOUNT = '{ record.WinnersCount}',
							SOLD = '{ record.Sold}',
							PRIZES = '{ record.Prizes}',
							PROFITS = '{ record.Profits}';
						";

						sql.AppendLine(recordToInsert);
					}
				}

				if (sql.Length > 0)
				{
					try
					{
						try
						{
							using (MySqlConnection connection = new MySqlConnection(connectionString))
							{
								connection.Open();
								using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
								{
									command.CommandType = CommandType.Text;
									command.ExecuteNonQuery();
								}
							}
						}
						catch (Exception e)
						{
							throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
						}

						WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql.ToString(), TABLE_DAILY_TOTAL_PROFIT2, "Lotto");
					}
					catch (Exception webhookEx)
					{
						Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql.ToString()}", webhookEx);
						throw;
					}
				}
			}

			

			protected override void SaveInTotalProfitByDrawingStorage(List<TotalProfitByDrawingRecord> totalProfitByDrawingRecords)
			{
				StringBuilder sql = new StringBuilder();

				if (totalProfitByDrawingRecords.Count > 0)
				{
					int groupsOfRowsToInsert = totalProfitByDrawingRecords.Count > MAXIMUM_NUMBER_OF_ROWS_TO_INSERT ? (totalProfitByDrawingRecords.Count / MAXIMUM_NUMBER_OF_ROWS_TO_INSERT) + 1 : MINIMUM_SET_OF_ROWS_TO_INSERT;
					for (int index = 0; index < groupsOfRowsToInsert; index++)
					{
						var rowsToSkip = index * MAXIMUM_NUMBER_OF_ROWS_TO_INSERT;
						foreach (TotalProfitByPicksDrawingRecord record in totalProfitByDrawingRecords.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_OF_ROWS_TO_INSERT))
						{
							string recordToInsert = $@"INSERT INTO {TABLE_TOTAL_PROFIT_BY_DRAWING}(DATE, STATE, GAMETYPE, DRAWINGID, TICKETSCOUNT, PLAYERSCOUNT, SOLD, PRIZES, PROFITS, AFFILIATEID, DOMAINID) VALUES 
								('{ ToDateString(record.DrawDate.Year, record.DrawDate.Month, record.DrawDate.Day, record.DrawDate.Hour, record.DrawDate.Minute, 0)}',
								'{ record.State}',
								'{ record.GameTypeForReports}',
								'{ record.DrawingId}',
								'{ record.TicketsCount}',
								'{ record.PlayersCount}',
								'{ record.Sold}',
								'{ record.Prizes}',
								'{ record.Profits}',
								{ record.AffiliateId},
								'{ record.DomainId}'
                            ) ON DUPLICATE KEY UPDATE 
								TICKETSCOUNT = '{ record.TicketsCount}',
								PLAYERSCOUNT = '{ record.PlayersCount}',
								SOLD = '{ record.Sold}',
								PRIZES = '{ record.Prizes}',
								PROFITS = '{ record.Profits}';
							";
							sql.AppendLine(recordToInsert);
						}
					}
				}

				if (sql.Length > 0)
				{
					using (MySqlConnection connection = new MySqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						catch (Exception e)
						{
							throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
						}
						finally
						{
							connection.Close();
						}
					}
				}
			}

			protected override void RemoveInDailyTotalProfitStorage(DateTime dateWithoutTime, string gameType)
			{
				if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_DAILY_TOTAL_PROFIT}' can not have hours or minutes.");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT} 
                                        WHERE DATE=@date AND GAMETYPE=@gameType";
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(dateWithoutTime));
							command.Parameters.AddWithValue("@gameType", gameType);
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}
			protected override void RemoveInDailyTotalProfitStorage2(DateTime dateWithoutTime, string gameType)
			{
				if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT2} 
                                        WHERE DATE=@date AND GAMETYPE=@gameType";
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(dateWithoutTime));
							command.Parameters.AddWithValue("@gameType", gameType);
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void RemoveInDailyTotalProfitStorageAt(DateTime day)
			{
				if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT2} 
                                        WHERE DATE(date)=@date";
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(day));
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void RemoveInTotalProfitByDrawingStorage(DateTime drawDate, string state, string gameType)
			{
				if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_TOTAL_PROFIT_BY_DRAWING} 
                                        WHERE DATE=@date AND STATE=@state AND GAMETYPE=@gameType";
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateTimeToString(drawDate));
							command.Parameters.AddWithValue("@state", state);
							command.Parameters.AddWithValue("@gameType", gameType);
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void RemoveInTotalProfitByDrawingStorageAt(DateTime day)
			{
				if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_TOTAL_PROFIT_BY_DRAWING}' can not have hours or minutes.");

				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_TOTAL_PROFIT_BY_DRAWING} 
                                        WHERE DATE(date)=@date";
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(day));
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override IEnumerable<TicketRecord> GenerateMoneyInvestedReport(DateTime startDate, DateTime endDate)
			{
				List<Tuple<DateTime, DateTime>> dateRangeList = new List<Tuple<DateTime, DateTime>>();
				dateRangeList.Add(Tuple.Create(startDate, endDate));
				var moneyInvested = TicketsPerDayReport(dateRangeList, string.Empty, string.Empty);
				var records = GenerateRecordsForMoneyInvestedReport(moneyInvested, startDate, endDate);
				return records.ToList();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsBetween(DateTime startDate, DateTime endDate)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}'
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsBetween(DateTime startDate, DateTime endDate)
			{
				var command = $@"select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_LOOSERS} T {COMMON_JOIN_FOR_TICKETS}
                    where DATE(date)>='{DateToString(startDate)}' and DATE(date)<='{DateToString(endDate)}'
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			private IEnumerable<TicketRecord> TicketsPerDayReport(List<Tuple<DateTime, DateTime>> dateRangeList, string gameType, string state)
			{
				var dateFilter = string.Empty;
				var lastDateRange = dateRangeList.Last();
				foreach (Tuple<DateTime, DateTime> dateRange in dateRangeList)
				{
					if (dateRange.Equals(lastDateRange)) { dateFilter = $@"(DATE BETWEEN '{DateTimeToString(dateRange.Item1)}' AND '{DateTimeToString(dateRange.Item2)}')"; }
					else { dateFilter = $@"(DATE BETWEEN '{DateTimeToString(dateRange.Item1)}' AND '{DateTimeToString(dateRange.Item2)}') AND "; }
				}

				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" AND LEFT(TICKET,2) ='{gameType}'";
				var stateFilter = String.IsNullOrWhiteSpace(state) ? "" : $" AND STATE ='{state}'";

				var commandWinners = $@"SELECT PRIZE, COUNT, AMOUNT, SUBSTRING(TICKET, 1, 2) AS GAMETYPE, CAST(DATE AS DATE) AS DATE
                                        FROM {TABLE_WINNERS}
                                        WHERE {dateFilter} {gameTypeFilter} {stateFilter}
											and timestamp = 0";

				var commandLosers = $@"SELECT 0 AS PRIZE, COUNT, AMOUNT, SUBSTRING(TICKET, 1, 2) AS GAMETYPE, CAST(DATE AS DATE) AS DATE
                                        FROM {TABLE_LOOSERS}
                                        WHERE {dateFilter} {gameTypeFilter} {stateFilter}
											and timestamp = 0";

				var command = $@"SELECT SUM(PRIZE) AS PRIZE, SUM(COUNT) AS COUNT, SUM(AMOUNT) AS AMOUNT, GAMETYPE, DATE FROM  ( {commandWinners} UNION ALL {commandLosers} ) AS ticket GROUP BY DATE, GAMETYPE";
				var tickets = Tickets(command);
				return tickets;
			}

			internal override CompletedLastPicksDraws LastPlayedDrawingsOfPlayer(string accountNumber)
			{
				var upperAccountNumber = accountNumber.ToUpper();
				var command = $@"SELECT DISTINCT * FROM 
								(
									(SELECT DISTINCT date, state
									FROM {TABLE_LOOSERS} 
									WHERE account = @accountNumber
									ORDER BY date DESC
									LIMIT 6
									)
								UNION ALL
									(SELECT DISTINCT date, state
									FROM {TABLE_WINNERS} 
									WHERE account = @accountNumber
									ORDER BY date DESC
									LIMIT 6
									)
								UNION ALL
									(SELECT DISTINCT date, state
									FROM {TABLE_NOACTIONS} 
									WHERE account = @accountNumber
									ORDER BY date DESC
									LIMIT 6
									)
								) T
                                ORDER BY date DESC
                                LIMIT 6;
                                ";
				var result = GetDrawings(command, upperAccountNumber);
				return result;
			}

			internal override void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
			{
				bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
				if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();

				var sql = $"SELECT ID FROM {TABLE_AFFILIATES} WHERE ID = '{id}'";
				try
				{
					try
					{
						using (MySqlConnection connection = new MySqlConnection(connectionString))
						{
							connection.Open();
							using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
							{
								bool itMustBeCreated = false;
								using (DbDataReader reader = command.ExecuteReader())
								{
									if (!reader.Read())
									{
										itMustBeCreated = true;
									}
								}
								if (itMustBeCreated)
								{
									sql = $"INSERT INTO {TABLE_AFFILIATES}(ID, NAME) VALUES ({id}, '{name}');";
									using (MySqlCommand command2 = new MySqlCommand(sql.ToString(), connection))
									{
										command2.CommandType = CommandType.Text;
										command2.ExecuteNonQuery();
									}
								}
							}

							sql = $"SELECT AFFILIATEID, ACCOUNT FROM {TABLE_USER_PER_AFFILIATE} WHERE ACCOUNT ='{accountNumber}' AND AFFILIATEID = {id};";
							using (MySqlCommand command = new MySqlCommand(sql.ToString(), connection))
							{
								bool itMustBeCreated = false;
								using (DbDataReader reader = command.ExecuteReader())
								{
									if (!reader.Read())
									{
										itMustBeCreated = true;
									}
								}

								if (itMustBeCreated)
								{
									sql = $"INSERT INTO {TABLE_USER_PER_AFFILIATE}(AFFILIATEID, ACCOUNT) VALUES ({id}, '{accountNumber}');";
									using (MySqlCommand command2 = new MySqlCommand(sql.ToString(), connection))
									{
										command2.CommandType = CommandType.Text;
										command2.ExecuteNonQuery();
									}
								}
							}
							connection.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
					}

					// After successful DB operation, send to webhook
					WebHookClientRequest.Instance.SendWebHook(DateTime.Now, $"INSERT AFFILIATE: ID={id}, NAME={name}, ACCOUNT={accountNumber}", "affiliates", "LottoAPI");
				}
				catch (Exception webhookEx)
				{
					Loggers.GetIntance().Webhook.Error($"Webhook failed for MySQL affiliate insert: ID={id}, NAME={name}, ACCOUNT={accountNumber}", webhookEx);
					throw;
				}
			}

			internal override List<AffiliateData> ListAffiliates()
			{
				List<AffiliateData> result = new List<AffiliateData>();

				var sql = $"SELECT ID, NAME FROM {TABLE_AFFILIATES} WHERE ID != {AffiliateData.DefaultAffiliateId} ;";
				using (MySqlConnection connection = new MySqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (MySqlCommand command = new MySqlCommand(sql, connection))
						using (DbDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								int id = reader.GetInt32(0);
								string name = reader.GetString(1);

								result.Add(new AffiliateData(id, name));
							}
							reader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
				return result;
			}
		}

		private class LottoDBHandlerSQLServer : LottoDBHandlerPicks
		{
			private const string COMMON_JOIN_FOR_TICKETS = @"INNER JOIN l900drawings DR WITH (nolock) ON T.DRAWINGID = DR.ID
                                                            INNER JOIN l900domains DO WITH (nolock) ON T.DOMAINID = DO.ID ";
			

			internal LottoDBHandlerSQLServer(string connectionString) : base(connectionString)
			{
				if (!ExistTable()) throw new GameEngineException($"There is no table {TABLE_LOOSERS}.");
                bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
                if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();
            }

			internal LottoDBHandlerSQLServer(Company company, string connectionString) : base(company, connectionString)
			{
				if (!ExistTable()) throw new GameEngineException($"There is no table {TABLE_LOOSERS}.");
                bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
                if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();
            }

			private bool ExistTable()
			{
				bool exists = false;
				StringBuilder statement = new StringBuilder();

				statement.AppendLine("IF EXISTS(")
					.AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
					.AppendLine("WHERE TABLE_NAME = '" + TABLE_LOOSERS + "')")
					.AppendLine("SELECT 1 ELSE SELECT 0;");
				string sql = statement.ToString();

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand command = new SqlCommand(sql, connection))
					{
						try
						{
							var result = (int)command.ExecuteScalar();
							exists = result == 1;
						}
						catch
						{
							exists = false;
						}
					}
					connection.Close();
				}
				return exists;
			}

			private string FullDateTimeToString(DateTime date)
			{
				string result = date.ToString("yyyy-MM-dd HH:mm:ss");
				return result;
			}

			protected override string DateTimeToString(DateTime date)
			{
				string result = date.ToString("yyyy-MM-dd HH:mm:ss.fff", Integration.CultureInfoEnUS);
				return result;
			}

			private string DrawingName(string command)
			{
				string drawingName = "";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							while (reader.Read())
							{
								drawingName = reader.GetString(0);
							}
						}
					}
					connection.Close();
				}
				return drawingName;
			}

			private IEnumerable<WinnerInfo> WinnerTickets(string command)
			{
				DateTime tempDate;
				List<WinnerInfo> winnerList = new List<WinnerInfo>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							WinnerInfo info;
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(1);
								info = new WinnerInfo(
									company: this.Company,
									draw: reader.GetString(6),
									fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                    gradedBy: reader.GetString(9),
									prize: reader.GetDecimal(18),
									stateAbb: reader.GetString(0),
									hour: tempDate.Hour,
									minute: tempDate.Minute,
									year: tempDate.Year,
									month: tempDate.Month,
									day: tempDate.Day,
									accountNumber: reader.GetString(2),
									ticket: reader.GetString(3),
									countOfTickets: reader.GetInt16(4),
									amount: reader.GetDecimal(5),
									selectionMode: (Ticket.Selection)reader.GetByte(8),
									action: (GameboardStatus)reader.GetByte(10),
									drawingId: reader.GetInt32(20),
									uniqueDrawingId: 0,
                                    position: (TypeNumberSequence)reader.GetByte(21),
                                    drawingName: reader.GetString(11),
									creation: reader.GetDateTime(12),
                                    orderNumber: reader.GetInt32(14),
                                    ticketNumber: reader.GetInt32(15),
									subticketsAndWagerNumbers: reader.GetString(16),
									profit: reader.GetDecimal(17),
									prizesVersion: reader.GetInt32(19),
									domainId: reader.GetInt16(22),
									domainUrl: reader.GetString(23),
                                    currencyId: reader.GetInt32(24)
                                );

								winnerList.Add(info);
							}
						}
					}
					connection.Close();
				}

				return winnerList;
			}

			private WinnerOrLooserRows WinnerTickets2(string command)
			{
				DateTime tempDate;
				WinnerOrLooserRows winners = new WinnerOrLooserRows();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							WinnerOrLooserRow info;
							while (reader.Read())
							{
								string accountNumber = reader.GetString(2);
								int affiliateId = reader.GetInt32(21);

								AffiliateWithAccount account = winners.AddAccount(accountNumber, affiliateId);

								tempDate = reader.GetDateTime(1);
								info = new WinnerOrLooserRow(
									stateAbb: reader.GetString(0),
									day: tempDate.Day,
									hour: tempDate.Hour,
									minute: tempDate.Minute,
									month: tempDate.Month,
									year: tempDate.Year,
									ticket: reader.GetString(3),
									countOfTickets: reader.GetInt16(4),
									amount: reader.GetDecimal(5),
									draw: reader.GetString(6),
									fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                    selectionMode: (Ticket.Selection)reader.GetByte(8),
									gradedBy: reader.GetString(9),
									action: (GameboardStatus)reader.GetByte(10),
									drawingId: reader.GetInt32(19),
									uniqueDrawingId: reader.GetInt32(20),
                                    position: (TypeNumberSequence)reader.GetByte(21),
                                    drawingName: reader.GetString(11),
									creation: reader.GetDateTime(12),
                                    orderNumber: reader.GetInt32(13),
                                    ticketNumber: reader.GetInt32(14),
									subticketsAndWagerNumbers: reader.GetString(15),
									profit: reader.GetDecimal(16),
									prize: reader.GetDecimal(17),
									account: account,
									domainId: reader.GetInt16(22),
									domainUrl: reader.GetString(23),
                                    currencyId: reader.GetInt32(24)
                                );

								winners.Add(info);
							}
						}
					}
					connection.Close();
				}

				return winners;
			}

			

			private IEnumerable<LoserInfo> LoserTickets(string command)
			{
				DateTime tempDate;
				List<LoserInfo> looserList = new List<LoserInfo>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							LoserInfo info;
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(1);
								info = new LoserInfo(
									company: this.Company,
									draw: reader.GetString(6),
									fireball: reader.IsDBNull(7) ? null : (int?)reader.GetInt16(7),
                                    gradedBy: reader.GetString(9),
									stateAbb: reader.GetString(0),
									hour: tempDate.Hour,
									minute: tempDate.Minute,
									year: tempDate.Year,
									month: tempDate.Month,
									day: tempDate.Day,
									accountNumber: reader.GetString(2),
									ticket: reader.GetString(3),
									countOfTickets: reader.GetInt16(4),
									amount: reader.GetDecimal(5),
									selectionMode: (Ticket.Selection)reader.GetByte(8),
									action: (GameboardStatus)reader.GetByte(10),
									drawingId: reader.GetInt32(19),
                                    position: (TypeNumberSequence)reader.GetByte(20),
                                    drawingName: reader.GetString(11),
									creation: reader.GetDateTime(12),
                                    orderNumber: reader.GetInt32(14),
                                    ticketNumber: reader.GetInt32(15),
									subticketsAndWagerNumbers: reader.GetString(16),
									profit: reader.GetDecimal(17),
									prizesVersion: reader.GetInt32(18),
									domainId: reader.GetInt16(21),
									domainUrl: reader.GetString(22),
                                    currencyId: reader.GetInt32(23)
                                );
								looserList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return looserList;
			}

			private IEnumerable<NoActionInfo> NoActionTickets(string command)
			{
				DateTime tempDate;
				List<NoActionInfo> notActionList = new List<NoActionInfo>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							NoActionInfo info;
							while (reader.Read())
							{
								tempDate = reader.GetDateTime(1);
								info = new NoActionInfo(
									company: this.Company,
									noActionBy: reader.GetString(14),
									stateAbb: reader.GetString(0),
									hour: tempDate.Hour,
									minute: tempDate.Minute,
									year: tempDate.Year,
									month: tempDate.Month,
									day: tempDate.Day,
									accountNumber: reader.GetString(2),
									ticket: reader.GetString(3),
									countOfTickets: reader.GetInt16(4),
									amount: reader.GetDecimal(5),
									selectionMode: (Ticket.Selection)reader.GetByte(6),
									action: (GameboardStatus)reader.GetByte(7),
									drawingId: reader.GetInt32(16),
                                    position: (TypeNumberSequence)reader.GetByte(17),
                                    drawingName: reader.GetString(8),
									creation: reader.GetDateTime(9),
                                    orderNumber: reader.GetInt32(11),
                                    ticketNumber: reader.GetInt32(12),
									subticketsAndWagerNumbers: reader.GetString(13),
									prizesVersion: reader.GetInt32(15),
									domainId: reader.GetInt16(18),
									domainUrl: reader.GetString(19),
                                    currencyId: reader.GetInt32(20)
                                );

								notActionList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return notActionList;
			}


			private List<DailyTotalProfitRecord2> DailyTotalProfitRecords2(string command)
			{
				var records = new List<DailyTotalProfitRecord2>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							DailyTotalProfitRecord2 info;
							while (reader.Read())
							{
								info = new DailyTotalProfitRecord2(
									date: reader.GetDateTime(0),
									gameType: reader.GetString(1),
									domainId: reader.GetInt16(7),
									affiliateId: reader.GetInt32(9),
                                    currencyId: reader.GetInt32(10),
                                    ticketsCount: reader.GetInt32(2),
									winnersCount: reader.GetInt32(3),
									sold: reader.GetDecimal(4),
									prizes: reader.GetDecimal(5),
									profits: reader.GetDecimal(6),
									domainUrl: reader.GetString(8)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command)
			{
				var records = new List<TotalProfitByDrawingRecord>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							TotalProfitByPicksDrawingRecord info;
							while (reader.Read())
							{
								info = new TotalProfitByPicksDrawingRecord(
									date: reader.GetDateTime(0),
									state: reader.GetString(1),
									gameType: reader.GetString(2),
									ticketsCount: reader.GetInt32(3),
									playersCount: reader.GetInt32(4),
									sold: reader.GetDecimal(5),
									prizes: reader.GetDecimal(6),
									profits: reader.GetDecimal(7),
									domainId: reader.GetInt32(8),
									domainUrl: reader.GetString(9),
									affiliateId: reader.GetInt32(10),
									drawingId: reader.GetInt32(11),
									position: (TypeNumberSequence)reader.GetByte(12),
                                    drawingName: reader.GetString(13)
								);
								records.Add(info);
							}
						}
					}
					connection.Close();
				}
				return records;
			}

			protected override CompletedLastPicksDraws GetDrawings(string command, string accountNumber)
			{
				var result = new CompletedLastPicksDraws();
				using (var connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (var cmd = new SqlCommand(command, connection))
					{
                        cmd.Parameters.AddWithValue("@accountNumber", accountNumber);
                        using (SqlDataReader reader = cmd.ExecuteReader())
						{
							CompletedPicksDraw info;
							while (reader.Read())
							{
								info = new CompletedPicksDraw(
									drawDate: reader.GetDateTime(0),
									state: reader.GetString(1)
								);
								var key = new CompletedDrawKey(info.DrawDate, info.State, string.Empty);
								result.Add(key, info);
							}
						}
					}
					connection.Close();
				}
				return result;
			}

			private IEnumerable<TicketRecord> Tickets(string command)
			{
				List<TicketRecord> ticketList = new List<TicketRecord>();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					connection.Open();
					using (SqlCommand cmd = new SqlCommand(command, connection))
					{
						using (SqlDataReader reader = cmd.ExecuteReader())
						{
							TicketRecord info;
							while (reader.Read())
							{
								info = new TicketRecord(reader.GetDecimal(0), reader.GetInt32(1), reader.GetDecimal(2), reader.GetString(3), reader.GetDateTime(4));
								ticketList.Add(info);
							}
						}
					}
					connection.Close();
				}
				return ticketList;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			private string DateSeparatedWithORs(List<DateTime> dates)
			{
				var linesForDate = new List<string>();
				foreach (var date in dates)
				{
					linesForDate.Add($"cast(date as date)='{DateToString(date)}'");
				}
				string dateSeparatedWithORs = "(" + string.Join(" or ", linesForDate) + ")";
				return dateSeparatedWithORs;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)<='{DateToString(endedDate)}' and cast(date as date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)<='{DateToString(endedDate)}' and cast(date as date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> GetPlayedTicketsBy(string ticketNumber)
			{
				var command = $@"
					({COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where ticketnumber='{ticketNumber.ToUpper()}' and timestamp = 0
                    union
					select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where ticketnumber='{ticketNumber.ToUpper()}' and timestamp = 0
                    union
					select state, date, account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where ticketnumber='{ticketNumber.ToUpper()}' and timestamp = 0)
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override WinnerInfo GetWinnerTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				var fullDateTimeToString = FullDateTimeToString(creationDate);
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and (creation BETWEEN '{fullDateTimeToString}:000' and '{fullDateTimeToString}:999') and cast(date as date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = WinnerTickets(command);
				return result.FirstOrDefault();
			}

			internal override WinnerInfo WinnerTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and cast(date as date)='{DateToString(drawDate)}' and ticketNumber='{ticketNumber.ToUpper()}' 
						and timestamp = 0
                    order by state, date;";
				var result = WinnerTickets(command);
				return result.FirstOrDefault();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)<='{DateToString(endedDate)}' and cast(date as date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)<='{DateToString(endedDate)}' and cast(date as date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override LoserInfo GetLoserTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				var fullDateTimeToString = FullDateTimeToString(creationDate);
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and (creation BETWEEN '{fullDateTimeToString}:000' and '{fullDateTimeToString}:999') and cast(date as date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = LoserTickets(command);
				return result.FirstOrDefault();
			}

			internal override LoserInfo LoserTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where state='{state}' and cast(date as date)='{DateToString(drawDate)}' and ticketNumber='{ticketNumber.ToUpper()}' 
						and timestamp = 0
                    order by state, date;";
				var result = LoserTickets(command);
				return result.FirstOrDefault();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where cast(date as date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where cast(date as date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where cast(date as date)<='{DateToString(endedDate)}' and cast(date as date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where cast(date as date)>='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where cast(date as date)='{DateToString(date)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				var dateSeparatedWithORs = DateSeparatedWithORs(dates);
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where account='{accountNumber.ToUpper()}' and 
                    {dateSeparatedWithORs}
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where cast(date as date)<='{DateToString(endedDate)}' and cast(date as date)>='{DateToString(startedDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override NoActionInfo GetNoActionTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				var fullDateTimeToString = FullDateTimeToString(creationDate);
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where state='{state}' and (creation BETWEEN '{fullDateTimeToString}:000' and '{fullDateTimeToString}:999') and cast(date as date)='{DateToString(drawDate)}' and account='{accountNumber.ToUpper()}' 
                    order by state, date;";
				var result = NoActionTickets(command);
				return result.FirstOrDefault();
			}

			internal override NoActionInfo NoActionTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} NA WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = NA.DRAWINGID
                    where state='{state}' and cast(date as date)='{DateToString(drawDate)}' and ticketNumber='{ticketNumber.ToUpper()}' 
						and timestamp = 0
                    order by state, date;";
				var result = NoActionTickets(command);
				return result.FirstOrDefault();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}   
                    where cast(date as date)<='{DateToString(endDate)}' and cast(date as date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS} 
                    where cast(date as date)<='{DateToString(endDate)}' and cast(date as date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerBetween(DateTime startDate, DateTime endDate, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)<='{DateToString(endDate)}' and cast(date as date)>='{DateToString(startDate)}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}   
                    where cast(date as date)='{DateToString(drawDate)}' and state='{state.ToUpper()}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_LOSERS}
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS} 
                    where cast(date as date)='{DateToString(drawDate)}' and state='{state.ToUpper()}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = LoserTickets(command);
				return result;
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				var command = $@"{COMMON_SELECT_FOR_NOACTION}
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)='{DateToString(drawDate)}' and state='{state.ToUpper()}' and account='{accountNumber.ToUpper()}' 
						and timestamp = 0 
                    order by date;";
				var result = NoActionTickets(command);
				return result;
			}

			internal override IEnumerable<WinnerInfo> TopWinnersForDrawing(DateTime drawDate, string state, string gameType, string domainUrl)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T {COMMON_JOIN_FOR_TICKETS}   
                    where cast(date as date)='{DateTimeToString(drawDate)}' and state='{state.ToUpper()}' and left(ticket,2) ='{gameType}' and url='{domainUrl}' 
						and timestamp = 0
                    order by prize desc;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" and T.account='{accountNumber.ToUpper()}'";
				var ticketAndWagerNumber = ticketNumber.Split('-');
				var ticketNumberFilter = String.IsNullOrWhiteSpace(ticketAndWagerNumber[0]) ? string.Empty : $" and ticketnumber={ticketAndWagerNumber[0]}";
				var wagerNumberFilter = ticketAndWagerNumber.Length == 1 ? string.Empty : $" and subticketsAndWagerNumbers like '%,{ticketAndWagerNumber[1]}%'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select state, date, T.account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(drawDate)}'  
						and timestamp = 0 
                    {accountNumberFilter} {ticketNumberFilter} {wagerNumberFilter} {gameTypeFilter} {domainIdFilter} {drawingIdFilter}";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			internal override CompletedPicksDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
				var report = GenerateRecordsForDrawingReport(winners, losers, noActions);
				return report;
			}

			internal override TicketsPerPlayersInCompletedPicksDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
				var report = GenerateRecordsForTicketsPerPlayersInDrawingReport(winners, losers, noActions);
				return report;
			}

			internal override WagersPerPlayerInCompletedDraw GenerateWagersPerPlayerInDrawingReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string fullTicketNumber, string domainIds)
			{
				var winners = WinnerTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, fullTicketNumber, domainIds);
				var losers = LoserTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, fullTicketNumber, domainIds);
				var noActions = NoActionTicketsForDrawingsReport(startDate, endDate, drawingId, accountNumber, gameType, fullTicketNumber, domainIds);
				var report = GenerateRecordsForWagersPerPlayerInDrawingReport(winners, losers, noActions, fullTicketNumber);
				return report;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var accountNumberFilter = String.IsNullOrWhiteSpace(accountNumber) ? "" : $" and T.account='{accountNumber.ToUpper()}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid, {AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' {accountNumberFilter} {domainIdFilter}
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			private IEnumerable<WinnerInfo> WinnerTicketsForWinnersOfTheMonthReport(DateTime date)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where MONTH(cast(date as date)) = {date.Month}
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			private string DrawingNameForTopWinnersReport(int drawId)
			{
				var command = $@"select drawingname
                    from {TABLE_DRAWINGS} DR
                    where ID = {drawId};";
				var name = DrawingName(command);
				return name;
			}

			internal override IEnumerable<PickWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				var winners = WinnerTicketsForWinnersReport(startDate, endDate, accountNumber, domainIds);
				var records = GenerateRecordsForWinnersReport(winners);
				return records.ToList();
			}

			public override IEnumerable<PickWinnerRecord> GenerateWinnersOfTheMonthReport(DateTime date)
			{
				var winners = WinnerTicketsForWinnersOfTheMonthReport(date);
				var records = GenerateRecordsForWinnersReport(winners);
				return records.ToList();
			}

			public override string DrawingNameFor(int drawId)
			{
				var name = DrawingNameForTopWinnersReport(drawId);
				return name;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var currencyIdFilter = string.Empty;
                if (!Enum.TryParse(Company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {Company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(Company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {Company.Lotto900().RewardCurrency} does not exist");
                if (currencyId == (int)currency)
                    currencyIdFilter = $" and (currencyid = {currencyId} or currencyid ={(int)rewardCurrency})";
                else if (currencyId != AllCurrencies)
                    currencyIdFilter = $" and currencyid = {currencyId}";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid 
					from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' {gameTypeFilter} {domainIdFilter} {currencyIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, prize, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(day)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var currencyIdFilter = string.Empty;
                if (!Enum.TryParse(Company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {Company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(Company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {Company.Lotto900().RewardCurrency} does not exist");
                if (currencyId == (int)currency)
                    currencyIdFilter = $" and (currencyid = {currencyId} or currencyid ={(int)rewardCurrency})";
                else if (currencyId != AllCurrencies)
                    currencyIdFilter = $" and currencyid = {currencyId}";

                var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' {gameTypeFilter} {domainIdFilter} {currencyIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var command = $@"select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"select state, date, T.account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(day)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and left(ticket,2) ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";

				var command = $@"select state, date, T.account, ticket, count, amount, '', selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
					from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
					where cast(date as date)='{DateToString(startDate)}' {gameTypeFilter} {domainIdFilter}
						and timestamp = 0
					order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";
				var command = $@"select state, date, account, ticket, count, amount, '{NO_DRAW}', NULL, selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where date='{DateTimeToString(drawDate)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				var stateFilter = String.IsNullOrWhiteSpace(state) ? string.Empty : $" and state ='{state}'";
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and left(ticket,2) ='{gameType}'";

				var command = $@"select state, date, T.account, ticket, count, amount, '', selection, noactionby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, 0.0, 0.0, prizesversion, drawingid, DR.uniqueid, DR.position, isnull(affiliateid,{AffiliateData.DefaultAffiliateId}), domainid, url, currencyid
                    from {TABLE_NOACTIONS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
					LEFT OUTER JOIN {TABLE_USER_PER_AFFILIATE} US on T.account = US.account
                    where cast(date as date)='{DateToString(day)}' {gameTypeFilter} {stateFilter}
						and timestamp = 0
                    order by date;";
				var winners = WinnerTickets2(command);
				var result = winners.ToWinnerInfo();
				return result;
			}

			protected override List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" and gametype ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
				var currencyIdFilter = string.Empty;
                if (!Enum.TryParse(Company.Lotto900().StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {Company.Lotto900().StandardCurrency} does not exist");
                if (!Enum.TryParse(Company.Lotto900().RewardCurrency, out Currencies.CODES rewardCurrency)) throw new GameEngineException($"Currency code {Company.Lotto900().RewardCurrency} does not exist");
                if (currencyId == (int)currency)
                    currencyIdFilter = $" and (currencyid = {currencyId} or currencyid ={(int)rewardCurrency})";
                else if (currencyId != AllCurrencies)
                    currencyIdFilter = $" and currencyid = {currencyId}";

                var command = $@"select date, gametype, ticketscount, winnerscount, sold, prizes, profits, domainid, url, affiliateid
					from {TABLE_DAILY_TOTAL_PROFIT2} T WITH (nolock) INNER JOIN {TABLE_DOMAINS} DO WITH (nolock) ON T.DOMAINID = DO.ID
					where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' {currencyIdFilter} {domainIdFilter} {gameTypeFilter}
					order by date;";
				var result = DailyTotalProfitRecords2(command);
				return result;
			}

			protected override List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string gameType, string uniqueDrawingId, string domainIds)
			{
				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? string.Empty : $" and gametype ='{gameType}'";
				var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" and domainid in ({domainIds})";
                var drawingIdFilter = string.IsNullOrWhiteSpace(uniqueDrawingId) ? string.Empty : $" and DR.uniqueid = '{uniqueDrawingId}'";

                var command = $@"select date, state, gametype, ticketscount, playerscount, sold, prizes, profits, domainid, url, affiliateid, DR.uniqueid, DR.position, drawingname
                    from {TABLE_TOTAL_PROFIT_BY_DRAWING} TPD WITH (nolock) INNER JOIN {TABLE_DRAWINGS} DR WITH (nolock) ON DR.ID = TPD.DRAWINGID
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}' 
                        {drawingIdFilter} {gameTypeFilter} {domainIdFilter} 
                    order by date;";

				var result = TotalProfitByDrawingRecords(command);
				return result;
			}

			internal override bool ExistsTable(string table)
			{
				bool exists = true;
				string sql = $"SELECT TOP 1 * FROM { table }";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							var dataReader = command.ExecuteReader();
							dataReader.Close();
						}
					}
					catch
					{
						exists = false;
					}
					finally
					{
						connection.Close();
					}
				}
				return exists;
			}

			internal override DateTime LastDateInDailyTotalProfit(string table)
			{
				var lastDate = new DateTime();
				var sql = $"SELECT TOP 1 DATE FROM {table} ORDER BY DATE DESC;";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						using (SqlDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								lastDate = reader.GetDateTime(0);
							}
							reader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
				return lastDate;
			}

			internal override DateTime LastDateInDailyTotalProfit()
			{
				if (!ExistsTable(TABLE_DAILY_TOTAL_PROFIT2)) throw new GameEngineException($"There is no table {TABLE_DAILY_TOTAL_PROFIT2} to get date");
				var lastDate = new DateTime();
				var sql = $"SELECT TOP 1 DATE FROM {TABLE_DAILY_TOTAL_PROFIT2} ORDER BY DATE DESC;";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						using (SqlDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								lastDate = reader.GetDateTime(0);
							}
							reader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
				return lastDate;
			}

			internal override DateTime LastDateInTotalProfitByDrawing()
			{
				if (!ExistsTable(TABLE_TOTAL_PROFIT_BY_DRAWING)) throw new GameEngineException($"There is no table {TABLE_TOTAL_PROFIT_BY_DRAWING} to get date");
				var lastDate = new DateTime();
				var sql = $"SELECT TOP 1 DATE FROM {TABLE_TOTAL_PROFIT_BY_DRAWING} ORDER BY DATE DESC;";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						using (SqlDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								lastDate = reader.GetDateTime(0);
							}
							reader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
				return lastDate;
			}

			protected override void CreateDailyTotalProfitStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement
					.AppendLine("IF NOT EXISTS(")
					.AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
					.AppendLine($"WHERE TABLE_NAME = '{TABLE_DAILY_TOTAL_PROFIT2}')")
					.AppendLine("BEGIN")
					.AppendLine($"CREATE TABLE {TABLE_DAILY_TOTAL_PROFIT2}")
					.AppendLine("(")
					.AppendLine("DATE DATETIME NOT NULL,")
					.AppendLine("GAMETYPE CHAR(2) NOT NULL,")
					.AppendLine("AFFILIATEID INT NOT NULL,")
					.AppendLine("TICKETSCOUNT INT NOT NULL,")
					.AppendLine("WINNERSCOUNT INT NOT NULL,")
					.AppendLine("SOLD DECIMAL(10,2) NOT NULL,")
					.AppendLine("PRIZES DECIMAL(10,2) NOT NULL,")
					.AppendLine("PROFITS DECIMAL(10,2) NOT NULL,")
					.AppendLine("DOMAINID SMALLINT NOT NULL DEFAULT 0,")
                    .AppendLine("CURRENCYID INT NOT NULL DEFAULT 2,")
                    .AppendLine("PRIMARY KEY ( DATE, CURRENCYID, DOMAINID, GAMETYPE, AFFILIATEID )")
					.AppendLine(");")
					.AppendLine("END");

				statement
					.AppendLine("IF NOT EXISTS(")
					.AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
					.AppendLine($"WHERE TABLE_NAME = '{TABLE_USER_PER_AFFILIATE}')")
					.AppendLine("BEGIN")
					.AppendLine($"CREATE TABLE {TABLE_USER_PER_AFFILIATE}")
					.AppendLine("(")
					.AppendLine($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
					.AppendLine("AFFILIATEID INT NOT NULL,")
					.AppendLine("PRIMARY KEY (ACCOUNT)")
					.AppendLine(");")
					.AppendLine("END");

				statement
					.AppendLine("IF NOT EXISTS(")
					.AppendLine("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
					.AppendLine($"WHERE TABLE_NAME = '{TABLE_AFFILIATES}')")
					.AppendLine("BEGIN")
					.AppendLine($"CREATE TABLE {TABLE_AFFILIATES}")
					.AppendLine("(")
					.AppendLine("ID INT IDENTITY(1,1) PRIMARY KEY,")
					.AppendLine("NAME VARCHAR(30) NOT NULL")
					.AppendLine(");")
					.AppendLine("END");

				string sql = statement.ToString();
				ExecuteCommand(sql);
			}

			protected override void ExecuteCommand(string cmdText)
			{
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(cmdText, connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						catch (Exception e)
						{
							Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
							throw new GameEngineException("SQLServer Error [" + cmdText + "]");
						}
						finally
						{
							connection.Close();
						}

						WebHookClientRequest.Instance.SendWebHook(DateTime.Now, cmdText, "UnknownAPI", "Lotto");
					}
					catch (Exception webhookEx)
					{
						Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {cmdText}", webhookEx);
						throw;
					}
				}
			}

			protected override void CreateTotalProfitByDrawingStorage()
			{
				StringBuilder statement = new StringBuilder();

				statement
					.AppendLine($"CREATE TABLE ").Append(TABLE_TOTAL_PROFIT_BY_DRAWING)
					.AppendLine("(")
					.AppendLine("DATE DATETIME NOT NULL,")
					.AppendLine("STATE CHAR(2) NOT NULL,")
					.AppendLine("GAMETYPE CHAR(2) NOT NULL,")
					.AppendLine("DRAWINGID INT NOT NULL,")
					.AppendLine("TICKETSCOUNT INT NOT NULL,")
					.AppendLine("PLAYERSCOUNT INT NOT NULL,")
					.AppendLine("SOLD DECIMAL(10,2) NOT NULL,")
					.AppendLine("PRIZES DECIMAL(10,2) NOT NULL,")
					.AppendLine("PROFITS DECIMAL(10,2) NOT NULL,")
					.AppendLine("AFFILIATEID INT NOT NULL DEFAULT 0,")
					.AppendLine("DOMAINID SMALLINT NOT NULL DEFAULT 0,")
					.AppendLine(");")
                    .AppendLine("CREATE INDEX IDX_DATE_DRAWINGID ON ").Append(TABLE_TOTAL_PROFIT_BY_DRAWING).Append(" (DATE, DRAWINGID);");

                string sql = statement.ToString();
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						{
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void SaveInDailyTotalProfitStorage(List<DailyTotalProfitRecord2> dailyTotalProfitRecords)
			{
				StringBuilder sql = new StringBuilder();

				if (dailyTotalProfitRecords.Count > 0)
				{
					var commandToInsert = $"INSERT INTO {TABLE_DAILY_TOTAL_PROFIT2}(DATE, GAMETYPE, TICKETSCOUNT, WINNERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID, CURRENCYID, AFFILIATEID) VALUES ";
					sql.Append(commandToInsert);
					var dailyTotalProfitValues = new List<string>();
					foreach (var record in dailyTotalProfitRecords)
					{
						string recordToInsert = $@"('{ ToDateString(record.Date.Year, record.Date.Month, record.Date.Day, 0, 0, 0)}',
						'{ record.GameType}',
						'{ record.TicketsCount}',
						'{ record.WinnersCount}',
						'{ record.Sold}',
						'{ record.Prizes}',
						'{ record.Profits}',
						'{ record.DomainId}',
						'{record.CurrencyId}',
						{record.AffiliateId}
						)";

						dailyTotalProfitValues.Add(recordToInsert);
					}

					sql.Append(string.Join(",", dailyTotalProfitValues));
					sql.Append(';');
				}

				if (sql.Length > 0)
				{
					try
					{
						try
						{
							using (SqlConnection connection = new SqlConnection(connectionString))
							{
								connection.Open();
								using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
								{
									command.CommandType = CommandType.Text;
									command.ExecuteNonQuery();
								}
							}
						}
						catch (Exception e)
						{
							throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
						}

						WebHookClientRequest.Instance.SendWebHook(DateTime.Now, sql.ToString(), TABLE_DAILY_TOTAL_PROFIT2, "Lotto");
					}
					catch (Exception webhookEx)
					{
						Loggers.GetIntance().Webhook.Error($"Webhook failed for SQL: {sql.ToString()}", webhookEx);
						throw;
					}
				}
			}

			internal override void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
			{
				bool tablesAlreadyExists = ExistsTable(TABLE_DAILY_TOTAL_PROFIT2);
				if (!tablesAlreadyExists) CreateDailyTotalProfitStorage();

				var sql = $"SELECT ID FROM {TABLE_AFFILIATES} WHERE ID = '{id}'";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
						{
							bool itMustBeCreated = false;

							using (SqlDataReader reader = command.ExecuteReader())
							{
								if (!reader.Read())
								{
									itMustBeCreated = true;
								}
							}
							if (itMustBeCreated)
							{
								sql = $"INSERT INTO {TABLE_AFFILIATES}(ID, NAME) VALUES ({id}, '{name}');";
								using (SqlCommand command2 = new SqlCommand(sql.ToString(), connection))
								{
									command2.CommandType = CommandType.Text;
									command2.ExecuteNonQuery();
								}
							}
						}

						sql = $"SELECT AFFILIATEID, ACCOUNT FROM {TABLE_USER_PER_AFFILIATE} WHERE ACCOUNT ='{accountNumber}' AND AFFILIATEID = {id} AND AFFILIATEID <> {AffiliateData.DefaultAffiliateId}; ";
						using (SqlCommand command = new SqlCommand(sql.ToString(), connection))
						{
							bool itMustBeCreated = false;
							using (SqlDataReader reader = command.ExecuteReader())
							{
								if (!reader.Read())
								{
									itMustBeCreated = true;
								}
							}

							if (itMustBeCreated)
							{
								sql = $"INSERT INTO {TABLE_USER_PER_AFFILIATE}(AFFILIATEID, ACCOUNT) VALUES ({id}, '{accountNumber}');";
								using (SqlCommand command2 = new SqlCommand(sql.ToString(), connection))
								{
									command2.CommandType = CommandType.Text;
									command2.ExecuteNonQuery();
								}
							}
						}

					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Server Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void SaveInTotalProfitByDrawingStorage(List<TotalProfitByDrawingRecord> totalProfitByDrawingRecords)
			{
				StringBuilder cmdText = new StringBuilder();

				if (totalProfitByDrawingRecords.Count > 0)
				{
					int groupsOfRowsToInsert = totalProfitByDrawingRecords.Count > MAXIMUM_NUMBER_OF_ROWS_TO_INSERT ? (totalProfitByDrawingRecords.Count / MAXIMUM_NUMBER_OF_ROWS_TO_INSERT) + 1 : MINIMUM_SET_OF_ROWS_TO_INSERT;
					var totalProfitValues = new List<string>();
					for (int index = 0; index < groupsOfRowsToInsert; index++)
					{
						var rowsToSkip = index * MAXIMUM_NUMBER_OF_ROWS_TO_INSERT;
						cmdText.Append($"INSERT INTO {TABLE_TOTAL_PROFIT_BY_DRAWING}(DATE, STATE, GAMETYPE, DRAWINGID, TICKETSCOUNT, PLAYERSCOUNT, SOLD, PRIZES, PROFITS, AFFILIATEID, DOMAINID) VALUES ");

						foreach (TotalProfitByPicksDrawingRecord record in totalProfitByDrawingRecords.Skip(rowsToSkip).Take(MAXIMUM_NUMBER_OF_ROWS_TO_INSERT))
						{
							string recordToInsert = $@"('{ ToDateString(record.DrawDate.Year, record.DrawDate.Month, record.DrawDate.Day, record.DrawDate.Hour, record.DrawDate.Minute, 0)}',
                            '{ record.State}',
                            '{ record.GameTypeForReports}',
                            '{ record.DrawingId}',
                            '{ record.TicketsCount}',
                            '{ record.PlayersCount}',
                            '{ record.Sold}',
                            '{ record.Prizes}',
                            '{ record.Profits}',
							{ record.AffiliateId},
							'{ record.DomainId}'
                            )";

							totalProfitValues.Add(recordToInsert);
						}

						cmdText.Append(string.Join(",", totalProfitValues));
						cmdText.Append(';');
						totalProfitValues.Clear();
					}
				}

				if (cmdText.Length > 0)
				{
					using (SqlConnection connection = new SqlConnection(connectionString))
					{
						try
						{
							connection.Open();
							using (SqlCommand command = new SqlCommand(cmdText.ToString(), connection))
							{
								command.CommandType = CommandType.Text;
								command.ExecuteNonQuery();
							}
						}
						catch (Exception e)
						{
							throw new GameEngineException($"SQL Server Error [{ cmdText.ToString() }]. {e.Message}");
						}
						finally
						{
							connection.Close();
						}
					}
				}
			}

			protected override void RemoveInDailyTotalProfitStorage(DateTime dateWithoutTime, string gameType)
			{
				if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT2} 
                                        WHERE DATE=@date AND GAMETYPE=@gameType";
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(dateWithoutTime));
							command.Parameters.AddWithValue("@gameType", gameType);
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void RemoveInDailyTotalProfitStorageAt(DateTime day)
			{
				if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT2} 
                                        WHERE cast(date as date)=@date";
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(day));
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void RemoveInTotalProfitByDrawingStorage(DateTime drawDate, string state, string gameType)
			{
				if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_TOTAL_PROFIT_BY_DRAWING} 
                                        WHERE DATE=@date AND STATE=@state AND GAMETYPE=@gameType";
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateTimeToString(drawDate));
							command.Parameters.AddWithValue("@state", state);
							command.Parameters.AddWithValue("@gameType", gameType);
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			protected override void RemoveInTotalProfitByDrawingStorageAt(DateTime day)
			{
				if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_TOTAL_PROFIT_BY_DRAWING}' can not have hours or minutes.");

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_TOTAL_PROFIT_BY_DRAWING} 
                                        WHERE cast(date as date) = @date";
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(day));
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}

			internal override IEnumerable<TicketRecord> GenerateMoneyInvestedReport(DateTime startDate, DateTime endDate)
			{
				List<Tuple<DateTime, DateTime>> dateRangeList = new List<Tuple<DateTime, DateTime>>();
				dateRangeList.Add(Tuple.Create(startDate, endDate));
				var moneyInvested = TicketsPerDayReport(dateRangeList, string.Empty, string.Empty);
				var records = GenerateRecordsForMoneyInvestedReport(moneyInvested, startDate, endDate);
				return records.ToList();
			}

			private IEnumerable<TicketRecord> TicketsPerDayReport(List<Tuple<DateTime, DateTime>> dateRangeList, string gameType, string state)
			{
				var dateFilter = string.Empty;
				var lastDateRange = dateRangeList.Last();
				foreach (Tuple<DateTime, DateTime> dateRange in dateRangeList)
				{
					if (dateRange.Equals(lastDateRange)) { dateFilter = $@"(DATE BETWEEN '{DateTimeToString(dateRange.Item1)}' AND '{DateTimeToString(dateRange.Item2)}')"; }
					else { dateFilter = $@"(DATE BETWEEN '{DateTimeToString(dateRange.Item1)}' AND '{DateTimeToString(dateRange.Item2)}') AND "; }
				}

				var gameTypeFilter = String.IsNullOrWhiteSpace(gameType) ? "" : $" AND LEFT(TICKET,2) ='{gameType}'";
				var stateFilter = String.IsNullOrWhiteSpace(state) ? "" : $" AND STATE ='{state}'";

				var commandWinners = $@"SELECT PRIZE, COUNT, AMOUNT, SUBSTRING(TICKET, 1, 2) AS GAMETYPE, CAST(CONVERT(VARCHAR(10), DATE, 120) AS DATETIME2(0)) AS DATE
	                                    FROM {TABLE_WINNERS} 
                                        WHERE {dateFilter} {gameTypeFilter} {stateFilter}";

				var commandLosers = $@"SELECT 0 AS PRIZE, COUNT, AMOUNT, SUBSTRING(TICKET, 1, 2) AS GAMETYPE, CAST(CONVERT(VARCHAR(10), DATE, 120) AS DATETIME2(0)) AS DATE
	                                FROM {TABLE_LOOSERS} 
                                    WHERE {dateFilter} {gameTypeFilter} {stateFilter}";

				var command = $@"SELECT SUM(PRIZE), SUM(COUNT) AS COUNT, SUM(AMOUNT) AS AMOUNT, GAMETYPE, DATE FROM ( {commandWinners} UNION ALL {commandLosers} ) AS TICKET GROUP BY DATE, GAMETYPE";

				var tickets = Tickets(command);
				return tickets;
			}

			internal override CompletedLastPicksDraws LastPlayedDrawingsOfPlayer(string accountNumber)
			{
                var upperAccountNumber = accountNumber.ToUpper();
                var command = $@"WITH CombinedData AS (
									SELECT date, state 
									FROM (
										SELECT DISTINCT TOP 6 date, state
										FROM {TABLE_LOOSERS} WITH (nolock)
										WHERE accountnumber = @accountNumber
										ORDER BY date DESC
									) AS T1
									UNION ALL
									SELECT date, state 
									FROM (
										SELECT DISTINCT TOP 6 date, state
										FROM {TABLE_WINNERS} WITH (nolock)
										WHERE accountnumber = @accountNumber
										ORDER BY date DESC
									) AS T2
									UNION ALL
									SELECT date, state 
									FROM (
										SELECT DISTINCT TOP 6 date, state
										FROM {TABLE_NOACTIONS} WITH (nolock)
										WHERE accountnumber = @accountNumber
										ORDER BY date DESC
									) AS T3
								) 
								SELECT TOP 6 *
								FROM CombinedData
								ORDER BY date DESC;
                                ";
				var result = GetDrawings(command, upperAccountNumber);
				return result;
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsBetween(DateTime startDate, DateTime endDate)
			{
				var command = $@"{COMMON_SELECT_FOR_WINNERS}
                    from {TABLE_WINNERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}'
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsBetween(DateTime startDate, DateTime endDate)
			{
				var command = $@"select state, date, account, ticket, count, amount, draw, fireball, selection, gradedby, action, drawingname, creation, ordernumber, ticketnumber, subticketsAndWagerNumbers, profit, 0.0, prizesversion, drawingid, DR.position, domainid, url, currencyid
                    from {TABLE_LOOSERS} T WITH (nolock) {COMMON_JOIN_FOR_TICKETS}
                    where cast(date as date)>='{DateToString(startDate)}' and cast(date as date)<='{DateToString(endDate)}'
						and timestamp = 0
                    order by date;";
				var result = WinnerTickets(command);
				return result;
			}

			protected override void RemoveInDailyTotalProfitStorage2(DateTime dateWithoutTime, string gameType)
			{
				if (dateWithoutTime.Hour != 0 || dateWithoutTime.Minute != 0) throw new GameEngineException($"Day to remove in table '{TABLE_DAILY_TOTAL_PROFIT2}' can not have hours or minutes.");
				if (!Reports.IsAValidReportGameType(gameType)) throw new GameEngineException($"{nameof(gameType)} '{gameType}' is not a valid game type for reports.");

				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT2} 
                                        WHERE DATE=@date AND GAMETYPE=@gameType";
						connection.Open();
						using (SqlCommand command = new SqlCommand(cmdText, connection))
						{
							command.Parameters.AddWithValue("@date", DateToString(dateWithoutTime));
							command.Parameters.AddWithValue("@gameType", gameType);
							command.ExecuteNonQuery();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"MySQL Error: {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
			}
			internal override List<AffiliateData> ListAffiliates()
			{
				List<AffiliateData> result = new List<AffiliateData>();

				var sql = $"SELECT ID, NAME FROM {TABLE_AFFILIATES} WHERE ID != {AffiliateData.DefaultAffiliateId} ;";
				using (SqlConnection connection = new SqlConnection(connectionString))
				{
					try
					{
						connection.Open();
						using (SqlCommand command = new SqlCommand(sql, connection))
						using (DbDataReader reader = command.ExecuteReader())
						{
							while (reader.Read())
							{
								int id = reader.GetInt32(0);
								string name = reader.GetString(1);

								result.Add(new AffiliateData(id, name));
							}
							reader.Close();
						}
					}
					catch (Exception e)
					{
						throw new GameEngineException($"SQL Error [{ sql.ToString() }]. {e.Message}");
					}
					finally
					{
						connection.Close();
					}
				}
				return result;
			}
		}

		

		public sealed class WinnerOrLooserRows: AffiliateAccounts
		{
			private List<WinnerOrLooserRow> winnerOrLooserRow = new List<WinnerOrLooserRow>();
			
			internal void Add(WinnerOrLooserRow info)
			{
				winnerOrLooserRow.Add(info);
			}

			internal IEnumerable<WinnerInfo> ToWinnerInfo()
			{
				List<WinnerInfo> result = new List<WinnerInfo>();
				foreach (WinnerOrLooserRow winner in winnerOrLooserRow)
				{
					result.Add(winner.ToWinnerInfo());
				}
				return result;
			}
		}

		

		private class LottoDBHandlerInMemory : LottoDBHandlerPicks
		{
			internal LottoDBHandlerInMemory() : base("-")
			{
			}

			internal LottoDBHandlerInMemory(Company company) : base(company, "-")
			{
			}

			public override string DrawingNameFor(int drawingId)
			{
				throw new NotImplementedException();
			}

			public override IEnumerable<PickWinnerRecord> GenerateWinnersOfTheMonthReport(DateTime date)
			{
				throw new NotImplementedException();
			}

			protected override string DateTimeToString(DateTime date)
			{
				throw new NotImplementedException();
			}

			internal override DateTime LastDateInDailyTotalProfit()
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsBetween(DateTime startDate, DateTime endDate)
			{
				throw new NotImplementedException();
			}

			protected override void RemoveInDailyTotalProfitStorage(DateTime drawDate, string gameType)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsBetween(DateTime startDate, DateTime endDate)
			{
				throw new NotImplementedException();
			}

			internal override CompletedPicksDraws GenerateDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<TicketRecord> GenerateMoneyInvestedReport(DateTime startDate, DateTime endDate)
			{
				throw new NotImplementedException();
			}

			internal override TicketsPerPlayersInCompletedPicksDraws GenerateTicketsPerPlayersInDrawingReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			internal override WagersPerPlayerInCompletedDraw GenerateWagersPerPlayerInDrawingReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string fullTicketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<PickWinnerRecord> GenerateWinnersReport(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			internal override LoserInfo GetLoserTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override LoserInfo LoserTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				throw new NotImplementedException();
			}

			internal override NoActionInfo GetNoActionTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override NoActionInfo NoActionTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				throw new NotImplementedException();
			}

			internal override WinnerInfo GetWinnerTicketBy(string state, DateTime creationDate, DateTime drawDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override WinnerInfo WinnerTicketBy(string state, DateTime drawDate, string ticketNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateAt(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateFrom(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByStateIn(List<DateTime> dates, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeAt(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeFrom(DateTime date, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerByTimeIn(List<DateTime> dates, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> WinnerTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<LoserInfo> LoserTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<NoActionInfo> NoActionTicketsOfPlayer(DateTime drawDate, string state, string accountNumber)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDrawingsReport(DateTime startDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDrawingsReport(DateTime startDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDrawingsReport(DateTime startDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override List<TotalProfitByDrawingRecord> TotalProfitByDrawingRecords(string command)
			{
				throw new NotImplementedException();
			}

			protected override CompletedLastPicksDraws GetDrawings(string command, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override DateTime LastDateInTotalProfitByDrawing()
			{
				throw new NotImplementedException();
			}

			protected override void CreateTotalProfitByDrawingStorage()
			{
				throw new NotImplementedException();
			}

			protected override List<TotalProfitByDrawingRecord> FilteredTotalProfitByDrawingRecords(DateTime startDate, DateTime endDate, string gameType, string drawingId, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override void SaveInTotalProfitByDrawingStorage(List<TotalProfitByDrawingRecord> dailyTotalProfitRecords)
			{
				throw new NotImplementedException();
			}

			protected override void RemoveInTotalProfitByDrawingStorage(DateTime drawDate, string state, string gameType)
			{
				throw new NotImplementedException();
			}

			protected override void RemoveInTotalProfitByDrawingStorageAt(DateTime day)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawing(DateTime drawDate, string gameType, string state)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForTotalProfitByDrawingAt(DateTime day, string gameType, string state)
			{
				throw new NotImplementedException();
			}

			internal override bool ExistsTable(string table)
			{
				throw new NotImplementedException();
			}

			internal override DateTime LastDateInDailyTotalProfit(string table)
			{
				throw new NotImplementedException();
			}

			protected override void CreateDailyTotalProfitStorage()
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> WinnerTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> LoserTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				throw new NotImplementedException();
			}

			protected override IEnumerable<WinnerInfo> NoActionTicketsForDailyTotalProfit2(DateTime startDate, string gameType, string domainIds)
			{
				throw new NotImplementedException();
			}


			protected override void RemoveInDailyTotalProfitStorage2(DateTime drawDate, string gameType)
			{
				throw new NotImplementedException();
			}

			protected override void RemoveInDailyTotalProfitStorageAt(DateTime drawDate)
			{
				throw new NotImplementedException();
			}

			internal override CompletedLastPicksDraws LastPlayedDrawingsOfPlayer(string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override void InsertAffiliateIfNotExist(int id, string name, string accountNumber)
			{
				throw new NotImplementedException();
			}

			internal override List<AffiliateData> ListAffiliates()
			{
				throw new NotImplementedException();
			}

			protected override List<DailyTotalProfitRecord2> FilteredDailyTotalProfitRecords2(DateTime startDate, DateTime endDate, string gameType, string domainIds, int currencyId)
			{
				throw new NotImplementedException();
			}

			internal override IEnumerable<WinnerInfo> GetPlayedTicketsBy(string ticketNumber)
			{
				throw new NotImplementedException();
			}

			protected override void ExecuteCommand(string cmdText)
            {
                throw new NotImplementedException();
            }

            protected override void SaveInDailyTotalProfitStorage(List<DailyTotalProfitRecord2> dailyTotalProfitRecords)
            {
                throw new NotImplementedException();
            }

			internal override IEnumerable<WinnerInfo> TopWinnersForDrawing(DateTime drawDate, string state, string gameType, string domainUrl)
			{
				throw new NotImplementedException();
			}
		}
	}

	public sealed class WinnerOrLooserRow
	{
		public WinnerOrLooserRow(string stateAbb, int day, int hour, int minute, int month, int year, string ticket, short countOfTickets, decimal amount, string draw, int? fireball, Ticket.Selection selectionMode, 
			string gradedBy, GameboardStatus action, int drawingId, int uniqueDrawingId, TypeNumberSequence position, string drawingName, DateTime creation, int orderNumber, int ticketNumber, string subticketsAndWagerNumbers, 
			decimal profit, decimal prize, AffiliateWithAccount account, int domainId, string domainUrl, int currencyId)
		{
			StateAbb = stateAbb;
			Day = day;
			Hour = hour;
			Minute = minute;
			Month = month;
			Year = year;
			Ticket = ticket;
			CountOfTickets = countOfTickets;
			Amount = amount;
			Draw = draw;
			Fireball = fireball == null ? LotteryDraw.WITHOUT_FIREBALL : fireball.Value;
			SelectionMode = selectionMode;
			GradedBy = gradedBy;
			Action = action;
			DrawingId = drawingId;
			UniqueDrawingId = uniqueDrawingId;
            Position = position;
            DrawingName = drawingName;
			Creation = creation;
            OrderNumber = orderNumber;
            TicketNumber = ticketNumber;
			SubticketsAndWagerNumbers = subticketsAndWagerNumbers;
			Profit = profit;
			Prize = prize;
			Account = account;
			DomainId = domainId;
			DomainUrl = domainUrl;
			CurrencyId = currencyId;
		}

		public string StateAbb { get; }
		public int Day { get; }
		public int Hour { get; }
		public int Minute { get; }
		public int Month { get; }
		public int Year { get; }
		public string Ticket { get; }
		public short CountOfTickets { get; }
		public decimal Amount { get; }
		public string Draw { get; }
        public int Fireball { get; }
        public Ticket.Selection SelectionMode { get; }
		public string GradedBy { get; }
		public GameboardStatus Action { get; }
		public int DrawingId { get; }
		public int UniqueDrawingId { get; }
        public TypeNumberSequence Position { get; }
        public string DrawingName { get; }
		public DateTime Creation { get; }
        public int OrderNumber { get; }
        public int TicketNumber { get; }
		public string SubticketsAndWagerNumbers { get; }
		public decimal Profit { get; }
		public decimal Prize { get; }
		public AffiliateWithAccount Account { get; }
		public int DomainId { get; }
		public string DomainUrl { get; }
		public int CurrencyId { get; }

		internal WinnerInfo ToWinnerInfo()
		{
			WinnerInfo info = new WinnerInfo(
				null,
				this.Draw,
				this.Fireball,
				this.GradedBy,
				this.Prize,
				this.StateAbb,
				this.Hour,
				this.Minute,
				this.Year,
				this.Month,
				this.Day,
				this.Account.Number,
				this.Ticket,
				this.CountOfTickets,
				this.Amount,
				this.SelectionMode,
				this.Action,
				DrawingId,
				UniqueDrawingId,
                Position,
                this.DrawingName,
				this.Creation,
                OrderNumber,
                this.TicketNumber,
				this.SubticketsAndWagerNumbers,
				Profit,
				0,
				DomainId,
				DomainUrl,
                CurrencyId
                )
			{
				AffiliateId = this.Account.AffiliateId
			};

			return info;
		}
	}

	
}
